# 展会补充文档功能说明

## 功能概述

本功能实现了AI创建展会后通过新文档补充某个模块内容的完整流程，包括：

1. 展会补充文档指定模块完善内容功能
2. 展会补充次数购买功能
3. AI自动识别模块和手动指定模块两种模式
4. 可选的知识库同步功能
5. 完整的套餐管理系统

## 数据库表结构

### 1. 展会补充文档记录表 (merchant_zhanhui_supplement_record)
- 记录每次文档补充的详细信息
- 支持多模块选择（JSON格式）
- 可配置是否同步到知识库

### 2. 解析次数套餐表 (merchant_zhanhui_parse_package)
- 管理解析次数套餐信息
- 支持套餐的增删改查和状态管理

### 3. 解析次数购买记录表 (merchant_zhanhui_parse_order)
- 记录套餐购买订单信息
- 支持微信和支付宝支付

### 4. 展会表新增字段
- parse_count: 展会剩余解析次数

## 核心功能流程

### 文档补充流程
1. 用户上传文档 → 创建补充记录 → 触发异步任务
2. 任务执行 → 知识库处理（可选） → AI内容识别 → 模块匹配 → FAQ生成
3. 次数消耗 → 检查剩余次数 → 消耗1次 → 更新展会次数

### 支付购买流程
1. 选择套餐 → 创建订单 → 发起支付
2. 支付成功 → 回调处理 → 增加展会解析次数

## API接口说明

### 用户端接口

- `POST /api/zhanhui/supplement/upload` - 上传文档补充
- `GET /api/zhanhui/supplement/history` - 获取补充历史记录
- `GET /api/zhanhui/supplement/detail` - 获取补充记录详情
- `GET /api/zhanhui/supplement/parse-packages` - 获取解析次数套餐列表
- `GET /api/zhanhui/supplement/get-parse-count` - 获取展会剩余解析次数
- `POST /api/zhanhui/supplement/buy-parse-package` - 购买解析次数套餐

## 请求参数说明

### 上传文档补充
```json
{
    "zhanhuiGuid": "展会GUID",
    "file": "上传的文件",
    "supplementType": 1, // 1-AI自动识别，2-手动指定模块
    "targetModules": ["模块1", "模块2"], // 手动指定时的目标模块数组
    "isSyncKnowledge": 1 // 1-是，2-否，是否同步到知识库
}
```

### 购买解析次数套餐
```json
{
    "zhanhuiGuid": "展会GUID",
    "packageGuid": "套餐GUID",
    "payType": 1 // 1-微信，2-支付宝
}
```

## 核心类说明

### 模型类
- `MerchantZhanhuiSupplementRecordModel` - 补充记录模型
- `MerchantZhanhuiParsePackageModel` - 套餐模型
- `MerchantZhanhuiParseOrderModel` - 订单模型

### 控制器类
- `app\zhanhui\controller\api\Supplement` - 用户端补充功能控制器

### 业务逻辑类
- `app\zhanhui\logic\api\SupplementLogic` - 用户端补充功能逻辑

### 任务类
- `app\zhanhui\task\ZhanhuiBcAiBuildTask` - AI处理任务类

## 部署说明

### 1. 数据库初始化
执行SQL文件：`migration/zhanhui/supplement_feature.sql`

### 2. 配置定时任务
配置定时任务执行 `ZhanhuiBcAiBuildTask`，建议每分钟执行一次

### 3. 文件上传目录
确保 `uploads/zhanhui/supplement/` 目录有写入权限

### 4. 支付配置
确保微信支付和支付宝支付配置正确

## 注意事项

1. **知识库同步**: 只有当 `is_sync_knowledge = 1` 时才会进行知识库相关操作
2. **模块识别**: AI只会为识别到的对应模块添加内容，未识别到的模块不会添加
3. **次数消耗**: 每次补充消耗1次解析次数，次数不足时无法进行补充
4. **权限验证**: 用户端接口会验证用户是否有对应展会的管理权限
5. **文件处理**: 支持多种文档格式，通过星火知识库进行文档解析

## 测试方法

运行测试脚本：`php test_supplement_feature.php`

该脚本会测试模型常量、状态文本获取等基础功能，并提供完整的API接口测试说明。
