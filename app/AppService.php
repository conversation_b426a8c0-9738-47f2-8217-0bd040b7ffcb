<?php

declare(strict_types=1);

namespace app;

use app\libraries\client\internal\InternalCall;
use app\libraries\utils\tokencheck\TokenCheckCall;
use think\helper\Arr;
use think\helper\Str;
use think\Queue;
use think\Service;
use think\Validate;

/**
 * 应用服务类
 */
class AppService extends Service
{
    public function register()
    {
        // 服务注册
        $this->app->bind('token_check', TokenCheckCall::class);
        $this->app->bind('internalcall', InternalCall::class);
        //异步任务相关
        $this->app->bind('queue', Queue::class);
        $this->app->bind('queue.failer', function () {
            $config = $this->app->config->get('queue.failed', []);
            $type = Arr::pull($config, 'type', 'none');
            $class = false !== strpos($type, '\\') ? $type : '\\think\\queue\\failed\\' . Str::studly($type);
            return $this->app->invokeClass($class, [$config]);
        });
        //验证码相关
        Validate::maker(function ($validate) {
            $validate->extend('captcha', function ($value) {
                return captcha_check($value);
            }, ':attribute错误!');
        });

    }

    /* 暂时不用
    public function boot()
    {
        // 服务启动
    }
    */
}
