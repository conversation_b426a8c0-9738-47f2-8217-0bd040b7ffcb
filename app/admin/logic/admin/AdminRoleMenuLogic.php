<?php
/*
 * @Author: 杨红兵
 * @Date: 2022-12-23 15:39:58
 * @Last Modified by: 杨红兵
 * @Last Modified time: 2022-12-23 17:19:38
 */

namespace app\admin\logic\admin;

use app\admin\models\AdminMenuModel;
use app\admin\models\AdminRoleMenuModel;
use TencentCloud\Monitor\V20180724\Models\Operator;
use Throwable;

class AdminRoleMenuLogic
{
    /**
     * 编辑角色绑定权限
     *
     * <AUTHOR>
     * @DateTime 2022-12-23 15:58:39
     *
     * @param integer $roleId
     * @param array $menuIds
     * @return array
     */
    public function bindRoleMenu(int $roleId, array $menuIds, string $operator): array
    {
        AdminRoleMenuModel::getInstance()->db()->startTrans();
        try {
            //先将该角色下的所有用户都设置为删除状态
            AdminRoleMenuModel::withTrashed()->where('role_id', $roleId)
                ->update(['deleted_at' => time()]);
            foreach ($menuIds as $menuId) {
                /**
                 * @var AdminRoleMenuModel $item
                 */
                $item = AdminRoleMenuModel::withTrashed()->where('role_id', $roleId)
                    ->where('menu_id', $menuId)
                    ->findOrEmpty();
                if ($item->deletedAt) {
                    //是被删除的状态
                    $item->restore();
                    $item->deletedAt = null;
                    $item->operator = $operator;
                    $item->save();
                } elseif ($item->isEmpty()) {
                    //需要新增
                    $item->roleId = $roleId;
                    $item->operator = $operator;
                    $item->menuId = $menuId;
                    $item->save();
                }
            }
            //当前角色物理删除所有标识为删除的数据
            AdminRoleMenuModel::withTrashed()->where('role_id', $roleId)
                ->where('deleted_at', '>', 0)
                ->delete();
            AdminRoleMenuModel::getInstance()->db()->commit();
        } catch (Throwable $ex) {
            AdminRoleMenuModel::getInstance()->db()->rollback();
            throw $ex;
        }
        return [];
    }

    /**
     * 获取角色绑定的权限列表
     *
     * <AUTHOR>
     * @DateTime 2022-12-23 16:55:39
     *
     * @param integer $roleId
     * @return array
     */
    public function getRoleMenuList(int $roleId): array
    {
        $list = AdminRoleMenuModel::getInstance()
            ->where('role_id', $roleId)
            ->select();
        $reList = $list->map(function (AdminRoleMenuModel $item) {
            /**
             * @var AdminMenuModel $menu
             */
            $menu = $item->menu;
            if (!empty($menu) && !$menu->isEmpty()) {
                return [
                    'sysId' => $menu['sysId'],
                    'menuKey' => $menu->menuKey,
                    'menuName' => $menu->menuName,
                ];
            }
        })->toArray();
        //需要过虑一次异常数据
        return array_values(array_filter($reList, function ($item) {
            return $item != null;
        }));
    }
}
