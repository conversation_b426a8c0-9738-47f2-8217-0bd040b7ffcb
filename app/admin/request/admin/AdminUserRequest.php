<?php

namespace app\admin\request\admin;

use app\Request;

class AdminUserRequest extends Request
{
    protected array $msgs = [
        'userName' => '用户名必填',
        'password' => '密码必填',
        'guid' => 'guid必填',
        'newPwd' => '新密码错误',
        'oldPwd' => '旧密码必填',
    ];

    protected function getRule(): array
    {
        return [
            'list' => [
               'adminType' => ['require'],
            ],
            'create' => [
                'adminType' => ['require'],
                'mobile' => ['require'],
                'userName' => ['require'],
                'password' => ['require'],
            ],
            'login' => [
                'userName' => ['require'],
                'password' => ['require'],
            ],
            'changePwd' => [
                'newPwd' => ['require', 'length:5,15'],
                'oldPwd' => ['require'],
            ],
            'getRoleTree' => [
                'guid' => ['require'],
            ],
            'changeState' => [
                'guid' => ['require'],
                'state' => ['require'],
            ],
            'listByRoleName' => [
                'roleName' => ['require'],
            ],
        ];
    }
}
