<?php
/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Time: 2023/7/22   14:02
 */


namespace app\command\test;

class ImageTest
{
    function makeSign($arr): string
    {
        $sercet = 'd9209742e02ce95a687b36b6419c652b'; // 您的秘钥
        //签名步骤一：按字典序排序参数
        ksort($arr);
        $string = $this->toUrlParams($arr);
        //签名步骤二：在string后加入KEY
        $string = $string . "&key=" . $sercet;
        //签名步骤三：MD5加密
        $string = md5($string);
        //签名步骤四：所有字符转为大写
        $result = strtoupper($string);
        return $result;
    }

    /**
     * 格式化参数格式化成url参数
     */
    function toUrlParams($arr): string
    {
        $buff = "";
        foreach ($arr as $k => $v) {
            if ($k != "sign" && $v != "" && !is_array($v)) {
                $buff .= $k . "=" . $v . "&";
            }
        }

        $buff = trim($buff, "&");
        return $buff;
    }

    function httpPost($url, $data)
    {
        $curl = curl_init(); // 启动一个CURL会话
        $headers = array(
            'Content-Type: application/json',
        );
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_URL, $url); // 要访问的地址
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // 对认证证书来源的检查
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false); // 从证书中检查SSL加密算法是否存在
        curl_setopt($curl, CURLOPT_POST, true); // 发送一个常规的Post请求
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data)); // Post提交的数据包
        curl_setopt($curl, CURLOPT_TIMEOUT, 30); // 设置超时限制防止死循环
        curl_setopt($curl, CURLOPT_HEADER, false); // 显示返回的Header区域内容
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); // 获取的信息以文件流的形式返回
        $result = curl_exec($curl); // 执行操作
        if (curl_errno($curl)) {
            return 'Error POST' . curl_error($curl);
        }
        curl_close($curl); // 关键CURL会话
        return $result; // 返回数据
    }

    public function doImg()
    {

        $appid = 'SYZ1689399157259539'; // appid
        $nonce_str = time(); // 随机字符串 不超过 32位
        $trade_no = '987654321'; // 您平台的唯一单号
        $sign_type = 'MD5'; // 签名类型
        $prompt = '动漫帅哥'; // ai绘画描述
        $number = 1; // 图片数量  最大4张
        $wide = 1024; // 图片宽  10-2048 px
        $height = 1024; // 图片高  10-2048 px

// 签名验证
        $data = [
            'appid' => $appid,
            'nonce_str' => $nonce_str,
            'trade_no' => $trade_no,
            'sign_type' => $sign_type,
            'prompt' => $prompt,
            'number' => $number,
            'wide' => $wide,
            'height' => $height,
        ];

        $data['sign'] = $this->makeSign($data);

        $url = 'http://open.deepcity.cn/AiImage';
        $res = $this->httpPost($url, $data);
        $res = json_decode($res, true);
        var_dump($res);
    }





}