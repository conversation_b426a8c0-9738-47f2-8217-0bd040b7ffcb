<?php
/*
 * @Author: 杨红兵
 * @Date: 2022-10-04 13:34:51
 * @Last Modified by: 杨红兵
 * @Last Modified time: 2022-10-05 10:37:35
 */

namespace app\command\traits;

use app\libraries\utils\command\InputStyle;
use app\libraries\utils\command\OutputStyle;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Style\SymfonyStyle;
use think\console\Input;
use think\console\Output;

trait CommandProcessBar
{
    /**
     * 自定义输入类
     *
     * @var InputStyle
     * <AUTHOR>
     * @DateTime 2022-10-04 17:07:30
     *
     */
    protected InputStyle $inputStyle;
    /**
     * 自定义输出类
     *
     * @var OutputStyle
     * <AUTHOR>
     * @DateTime 2022-10-04 16:52:08
     *
     */
    protected OutputStyle $outputStyle;

    /**
     * 初始化
     * @param Input  $input  An InputInterface instance
     * @param Output $output An OutputInterface instance
     */
    protected function initialize(Input $input, Output $output)
    {
        unset($input, $output);
        $this->inputStyle = new InputStyle($this->input);
        $this->outputStyle = new OutputStyle($this->output);
    }

    /**
     * 创建进度条
     *
     * <AUTHOR>
     * @DateTime 2022-10-04 17:54:14
     *
     * @param integer $itemCount 进度度总数量
     * @param int $refreshTime 进度条刷新时间间隔
     * @return ProgressBar
     */
    protected function createProcessBar(int $itemCount, $refreshTime = 0.5): ProgressBar
    {
        $style = new SymfonyStyle($this->inputStyle, $this->outputStyle);
        $bar = $style->createProgressBar($itemCount);
        $bar->maxSecondsBetweenRedraws($refreshTime);
        $bar->setBarWidth(100);
        $bar->setOverwrite(true);
        return $bar;
    }
}
