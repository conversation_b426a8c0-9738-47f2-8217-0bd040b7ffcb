<?php

/**
 * @author: xuzhengyang
 * @Time: 2023/4/2   21:47
 */

namespace app\index\controller;

use app\BaseController;
use app\index\logic\CallbackLogic;
use app\libraries\response\Text;
use app\Request;
use think\Response;

class Callback extends BaseController
{
    /**
     * 支付回调地址-数字人充值回调
     * @return void
     */
    public function payNotify(Request $request, CallbackLogic $logic): void
    {
         $logic->channelNotify($request->getContent());
    }

    /**
     * 支付回调地址-AI点数充值回调
     * @return Response
     */
    public function payChatNotify(Request $request, CallbackLogic $logic)
    {
        return $logic->chatGoodsNotify($request->getContent());
    }

    public function alipayNotify()
    {
        LogInfo('alipayNotice', '支付宝异步通知2', '接受通知参数', ['data' => request()->post()]);
        return Text::create('success');
    }

    public function alipayChatNotify(CallbackLogic $logic)
    {
        LogInfo('alipayNotice', '支付宝异步通知3', '接受通知参数', ['data' => request()->post()]);
        $data = request()->post();
        return $logic->chatGoodsAlipayNotify($data);
    }

    public function alipayChannelNotify(CallbackLogic $logic)
    {
        LogInfo('alipayNotice', '支付宝异步通知3', '接受通知参数', ['data' => request()->post()]);
        $data = request()->post();
        return $logic->alipayChannelNotify($data);
    }

    /**
     * 会员卡充值回调-微信小程序
     * @return Response
     */
    public function payMemberNotify(CallbackLogic $logic)
    {
        LogInfo('payMemberNotify', '支付宝异步通知', '接受通知参数', ['data' => request()->post()]);
        $xml = simplexml_load_string(request()->getContent(), 'SimpleXMLElement', LIBXML_NOCDATA);
        $orderNo = (string)$xml->out_trade_no;
        return $logic->memberNotify(['orderNo' => $orderNo]);
    }

    /**
     * 会员卡充值回调-支付宝
     * @return Response
     */
    public function alipayMemberNotify(CallbackLogic $logic)
    {
        LogInfo('alipayNotice', '支付宝异步通知2', '接受通知参数', ['data' => request()->post()]);
        $orderNo = request()->post('out_trade_no');
        return $logic->memberNotify(['orderNo' => $orderNo]);
    }

    public function mjImgNotify(CallbackLogic $logic)
    {
        return $logic->mjImgNotify(request()->post());
    }

    /**
     * 展会购买回调
     * @param CallbackLogic $logic
     * @return Response
     */
    public function zhanhuiBuyNotify(CallbackLogic $logic)
    {
        LogInfo('zhanhuiBuyNotify', '展会购买回调', '接受通知参数', ['data' => request()->post()]);
        $xml = simplexml_load_string(request()->getContent(), 'SimpleXMLElement', LIBXML_NOCDATA);
        $orderNo = (string)$xml->out_trade_no;
        return $logic->zhanhuiBuyNotify(['orderNo' => $orderNo]);
    }

    /**
     * 展会解析套餐购买回调 - 微信支付
     * @param CallbackLogic $logic
     * @return Response
     */
    public function zhanhuiParsePackageNotify(CallbackLogic $logic)
    {
        LogInfo('zhanhuiParsePackageNotify', '展会解析套餐购买回调', '接受通知参数', ['data' => request()->post()]);
        $xml = simplexml_load_string(request()->getContent(), 'SimpleXMLElement', LIBXML_NOCDATA);
        $orderNo = (string)$xml->out_trade_no;
        return $logic->zhanhuiParsePackageNotify(['orderNo' => $orderNo]);
    }

    /**
     * 展会解析套餐购买回调 - 支付宝
     * @param CallbackLogic $logic
     * @return Response
     */
    public function zhanhuiParsePackageAlipayNotify(CallbackLogic $logic)
    {
        LogInfo('zhanhuiParsePackageAlipayNotify', '展会解析套餐支付宝购买回调', '接受通知参数', ['data' => request()->post()]);
        $data = request()->post();
        return $logic->zhanhuiParsePackageAlipayNotify($data);
    }
}
