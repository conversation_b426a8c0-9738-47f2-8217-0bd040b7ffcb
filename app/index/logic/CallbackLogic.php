<?php

/**
 * @author: xuz<PERSON><PERSON><PERSON>
 * @Time: 2023/4/6   23:25
 */

namespace app\index\logic;

use app\libraries\exception\ApiException;
use app\libraries\response\Text;
use app\libraries\utils\CosService;
use app\libraries\utils\Util;
use app\square\logic\api\ChatGoodsLogic;
use app\user\cache\UserCache;
use app\user\logic\api\ChannelLogic;
use app\user\models\UserImageOrderModel;
use app\user\models\UserWorksRecordModel;
use app\zhanhui\logic\api\OrderLogic;
use Monolog\Handler\IFTTTHandler;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Response;
use app\user\logic\api\MemberLogic;

class CallbackLogic
{
    /**
     * 数字人认证优化
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     * @throws ApiException
     */
    public function channelNotify($data)
    {

        $xml = simplexml_load_string($data, 'SimpleXMLElement', LIBXML_NOCDATA);
        //执行订单查询操作
        $queryLogic = new ChannelLogic();
        $ret = $queryLogic->queryPayChannel(['orderNo' => $xml->out_trade_no]);
        if ($ret['isPay']) {
            return Response::create(
                json_encode(['code' => 'SUCCESS', 'message' => '成功']),
                Text::class,
                200
            );
        }
        return Response::create(
            json_encode(['code' => 'fail', 'message' => '处理失败请重试']),
            Text::class,
            200
        );
    }

    public function chatGoodsNotify($data)
    {
        $xml = simplexml_load_string($data, 'SimpleXMLElement', LIBXML_NOCDATA);
        LogInfo('payNotify', '聊天点数回调', '进入回调', ['ret' => $xml]);
        //执行订单查询操作
        $queryLogic = new ChatGoodsLogic();
        $ret = $queryLogic->buyQuery(['orderNo' => $xml->out_trade_no]);
        if ($ret['isPay']) {
            return Response::create(
                json_encode(['code' => 'SUCCESS', 'message' => '成功']),
                Text::class,
                200
            );
        } else {
            LogError('payNotify', '聊天点数回调', '回调失败', ['ret' => $ret]);
        }
        return Response::create(
            json_encode(['code' => 'fail', 'message' => '处理失败请重试']),
            Text::class,
            200
        );
    }

    /**
     * 支付宝回调异步处理
     * @param $data
     * @return Response
     * @throws ApiException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function chatGoodsAlipayNotify($data)
    {
        LogInfo('payNotify', '聊天点数回调', '进入回调', ['ret' => $data]);
        //执行订单查询操作
        $queryLogic = new ChatGoodsLogic();
        $ret = $queryLogic->buyQuery(['orderNo' => $data['out_trade_no']]);
        LogInfo('payNotify', '聊天点数回调', '查询结果', ['ret' => $ret]);
        if ($ret['isPay']) {
            return Text::create('success');
        } else {
            LogError('payNotify', '聊天点数回调', '回调失败', ['ret' => $ret]);
        }
        return Text::create('fail');
    }

    public function alipayChannelNotify($data)
    {
        LogInfo('payNotify', '数字人认证点数回调', '进入回调', ['ret' => $data]);
        //执行订单查询操作
        $queryLogic = new ChannelLogic();
        $ret = $queryLogic->queryPayChannel(['orderNo' => $data['out_trade_no']]);
        if ($ret['isPay']) {
            return Text::create('success');
        }
        LogInfo('payNotify', '聊天点数回调', '查询结果', ['ret' => $ret]);
        return Text::create('fail');
    }

    /**
     * 会员卡购买回调
     * @param $data
     * @return Response
     * @throws ApiException
     * @throws DbException
     */
    public function memberNotify($data)
    {
        $queryLogic = new MemberLogic();
        $ret = $queryLogic->memberCardBuyQuery($data);
        if ($ret['isPay']) {
             return Text::create('success');
        }
        return Text::create('fail');
    }

    /**
     * 展会购买回调
     * @param $data
     * @return Response
     * @throws ApiException
     * @throws DbException
     */
    public function zhanhuiBuyNotify($data)
    {
        $queryLogic = new OrderLogic();
        $ret = $queryLogic->queryOrder($data);
        if ($ret['isPay']) {
            return Text::create('success');
        }
        return Text::create('fail');
    }

    public function mjImgNotify($data)
    {
        LogInfo('mjImgNotify', 'mj绘画回调', '接受通知参数', ['data' => $data]);
        $msgId = $data['id'] ?? null;
        $msgStatus = $data['status'] ?? null;
        if (!$msgId || !$msgStatus) {
            return Text::create('fail');
        }

        $imgOrder = UserImageOrderModel::getInstance()->where('msg_unique_id', $msgId)->findOrEmpty();
        if ($imgOrder->isEmpty()) {
            return Text::create('fail');
        }
        if ($imgOrder['order_status'] !== UserImageOrderModel::STATUS_DOING) {
            return Text::create('success');
        }
        if ($data['status'] !== 'SUCCESS') {
            // 判断进度是否大于80% 如果大于80%则休眠3秒后，主动查询一次
            $progress = $data['progress'] ?? "0%";
            $progress = intval(str_replace('%', '', $progress));
            if ($progress > 80) {
                sleep(3);
                $this->checkMidjourneyStatus($msgId);
            }
            return Text::create('doing');
        }
        // 这里开始操作可以加锁
        $msgLockKey = 'midjourney_msg_lock_' . $msgId;
        $lock = UserCache::getInstance()->lock($msgLockKey, 30);
        if (!$lock) {
            return Text::create('fail');
        }
        // 储存图片到远端
        $cosImgs =  CosService::upload($data['imageUrl'], 'image/png', 'image', '.png');
        $imgUrl = $cosImgs['data'] ?? $data['imageUrl'];
        $imgUrl = [$imgUrl];
        // 更新订单状态
        $imgOrder->orderStatus = UserImageOrderModel::STATUS_SUCCESS;
        $imgOrder->imageResult = $imgUrl;
        // 更新mj图像需要变换的参数
        if (!$imgOrder->imgChangeInfo) {
            $imgChangeInfo = [
                'use_U' => [],
                'use_V' => [],
                'able_U' => [
                    'components' => [
                        [
                            'label' => 'U1',
                            'custom_id' => 'U-1',
                        ],
                        [
                            'label' => 'U2',
                            'custom_id' => 'U-2',
                        ],
                        [
                            'label' => 'U3',
                            'custom_id' => 'U-3',
                        ],
                        [
                            'label' => 'U4',
                            'custom_id' => 'U-4',
                        ],
                    ]
                ],
                'able_V' => [
                    'components' => [
                        [
                            'label' => 'V1',
                            'custom_id' => 'V-1',
                        ],
                        [
                            'label' => 'V2',
                            'custom_id' => 'V-2',
                        ],
                        [
                            'label' => 'V3',
                            'custom_id' => 'V-3',
                        ],
                        [
                            'label' => 'V4',
                            'custom_id' => 'V-4',
                        ],
                    ]
                ]
            ];
            $imgOrder->imgChangeInfo = json_encode($imgChangeInfo, JSON_UNESCAPED_UNICODE);
        }
        $imgOrder->save();
        //修改工作状态为成功
        $recordInfo = UserWorksRecordModel::getInstance()
            ->where('work_type', UserWorksRecordModel::WORK_TYPE_IMAGE)
            ->where('order_no', $imgOrder['orderNo'])
            ->findOrEmpty();
        if (!$recordInfo->isEmpty()) {
            $recordInfo->workStatus = UserWorksRecordModel::STATUS_SUCCESS;
            $recordInfo->workResult = json_encode($imgUrl, JSON_UNESCAPED_UNICODE);
            $recordInfo->save();
        }
        UserCache::getInstance()->unlock($msgLockKey, $lock);
        return Text::create('success');
    }

    private function checkMidjourneyStatus($msgUniqueId)
    {
        try {
            $midjourneyUrl = config('chatgpt.midjourney_url') . "/mj/task/{$msgUniqueId}/fetch";
            $headers  = [
                'Content-Type: application/json',
            ];
            // 发起get请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_URL, $midjourneyUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_TIMEOUT, 1200); // 设置超时限制防止死循环
            $result =  curl_exec($ch);

            $result = json_decode($result, true);
            LogInfo('midjourney_query', 'midjourney脚本查询', 'midjourney查询任务查询结果', ['result' => $result]);
            if ($result) {
                (new CallbackLogic())->mjImgNotify($result);
            }
        } catch (\Throwable $exception) {
            LogError(
                'userWork-ai-image',
                '查询midjourney状态',
                '查询midjourney状态-异常-' . $exception->getMessage(),
                [
                    'e' => Util::normalizeException($exception),
                ]
            );
        }
    }
}
