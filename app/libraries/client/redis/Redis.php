<?php
/*
 * @Author: 杨红兵 
 * @Date: 2022-09-07 08:41:07 
 * @Last Modified by: 杨红兵
 * @Last Modified time: 2022-09-08 08:44:45
 */

namespace app\libraries\client\redis;

use think\cache\driver\Redis as DriverRedis;
use think\swoole\App;
use Throwable;

class Redis extends DriverRedis
{
    /**
     * 架构函数
     * @access public
     * @param array $options 缓存参数
     */
    public function __construct(array $options = [])
    {
        if (!empty($options)) {
            $this->options = array_merge($this->options, $options);
        }
        $this->reConnection();
    }

    /**
     * 连接 Redis
     *
     * <AUTHOR>
     * @DateTime 2021-10-12 15:00:06
     *
     * @return void
     */
    public function reConnection()
    {
        if (extension_loaded('redis')) {
            $this->connectRedis();
        } elseif (class_exists('\Predis\Client')) {
            $params = [];
            foreach ($this->options as $key => $val) {
                if (in_array(
                    $key,
                    ['aggregate', 'cluster', 'connections', 'exceptions', 'prefix', 'profile', 'replication', 'parameters']
                )) {
                    $params[$key] = $val;
                    unset($this->options[$key]);
                }
            }

            if ('' == $this->options['password']) {
                unset($this->options['password']);
            }
            $this->handler = new \Predis\Client($this->options, $params);
            $this->options['prefix'] = '';
        } else {
            throw new \BadFunctionCallException('not support: redis');
        }
        if (0 != $this->options['select']) {
            $this->handler->select((int) $this->options['select']);
        }
    }

    /**
     * PHP驱动连接Redis
     *
     * <AUTHOR>
     * @DateTime 2022-09-08 08:44:09
     *
     * @return void
     */
    protected function connectRedis()
    {
        if (app() instanceof App) {
            $this->options['persistent'] = false;
        }
        if ($this->handler != null) {
            try {
                $this->handler->close();
            } catch (Throwable $ex) {
                unset($ex);
            }
        }
        $this->handler = new \Redis();
        if ($this->options['persistent']) {
            if (strpos($this->options['host'], '/') !== false) {
                $this->handler->pconnect($this->options['host']);
            } else {
                $this->handler->pconnect(
                    $this->options['host'],
                    (int) $this->options['port'],
                    (int) $this->options['timeout'],
                    'persistent_id_' . $this->options['select']
                );
            }
        } else {
            if (strpos($this->options['host'], '/') !== false) {
                $this->handler->connect($this->options['host']);
            } else {
                $this->handler->connect($this->options['host'], (int) $this->options['port'], (int) $this->options['timeout']);
            }
        }

        if ('' != $this->options['password']) {
            $this->handler->auth($this->options['password']);
        }
    }

    /**
     * 返回底层的序列化数据
     *
     * <AUTHOR>
     * @DateTime 2022-03-24 14:54:25
     *
     * @param mixed $data
     * @return string
     */
    public function serialize($data): string
    {
        return parent::serialize($data);
    }

    /**
     * 返回底层的反序列化数据
     *
     * <AUTHOR>
     * @DateTime 2022-03-24 14:54:25
     *
     * @param string $data
     * @return mixed
     */
    public function unserialize($data)
    {
        return parent::unserialize($data);
    }

    /**
     * 执行lua脚本
     * @param $script
     * @param array $args
     * @param int $numKeys
     * @return mixed
     * @throws Throwable
     */
    public function eval($script, array $args = [], int $numKeys = 0)
    {
        if (!empty($args[0]) && is_string($args[0])) {
            $args[0] = $this->getCacheKey($args[0]);
        }
        $params = [
            $script,
            $args,
            $numKeys
        ];
        try {
            return call_user_func_array([$this->handler, 'eval'], $params);
        } catch (Throwable $ex) {
            if (strripos($ex->getMessage(), "went away") !== false && method_exists($this, 'reConnection')) {
                //说明有远程断线的情况，需要重连
                $this->handler->close();
                $this->reConnection();
                return call_user_func_array([$this->handler, 'eval'], $params);
            }
            throw $ex;
        }
    }

    public function __call($method, $args)
    {
        if (!empty($args[0]) && is_string($args[0])) {
            $args[0] = $this->getCacheKey($args[0]);
        }
        try {
            return call_user_func_array([$this->handler, $method], $args);
        } catch (Throwable $ex) {
            if (strripos($ex->getMessage(), "went away") !== false && method_exists($this, 'reConnection')) {
                //说明有远程断线的情况，需要重连
                $this->handler->close();
                $this->reConnection();
                return call_user_func_array([$this->handler, $method], $args);
            }
            throw $ex;
        }
    }
}
