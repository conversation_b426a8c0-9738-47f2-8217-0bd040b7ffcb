<?php

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @Time: 2023/7/17   20:06
 */

namespace app\libraries\service\ai_open\ai_chat;

use app\libraries\service\ai_open\ai_chat\entity\ChatCompleteEntity;

abstract class AiChatAbstract
{
    abstract public function chatComplete(ChatCompleteEntity $entity): array;

    /**
     * 获取当前时间的毫秒数作为消息id
     * @return float
     */
    public function getCurrentMilliseconds()
    {
        $time = microtime(true); // 获取当前时间的浮点数表示，包括微秒
        $milliseconds = round($time * 1000); // 将浮点数表示的时间转换为毫秒
        return $milliseconds;
    }
}
