<?php

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @Time: 2023/7/19   03:36
 */

namespace app\libraries\service\ai_open\ai_chat\lmp;

use app\libraries\service\ai_open\ai_chat\AiChatAbstract;
use app\libraries\service\ai_open\ai_chat\entity\ChatCompleteEntity;
use Firebase\JWT\JWT;

/**
 * 清华智谱模型，接口地址：https://open.bigmodel.cn/doc/api#chatglm_std
 */
class ZhipuChat extends AiChatAbstract
{
    public $content = '';

    public function chatComplete(ChatCompleteEntity $entity): array
    {
        if (empty($entity->modelSign)) {
            $entity->modelSign = 'glm-4-flashx';
        }

        return $this->streamChatCompleteV2($entity);
    }


    private function getJwtToken()
    {
        //需安装扩展：composer require firebase/php-jwt
        [$key, $secret] = explode('.', config('chatgpt.qinghua_api_key'));
        $payload = [
            'api_key' => $key,
            'exp' => $this->getCurrentMilliseconds() + 600 * 1000,  //毫秒
            'timestamp' => $this->getCurrentMilliseconds(), //毫秒
        ];
        return JWT::encode($payload, $secret, 'HS256', null, ["alg" => "HS256", "sign_type" => "SIGN"]);
    }

    /**
     * 非流式返回聊天内容
     * @param ChatCompleteEntity $entity
     * @return array
     * @throws \Exception
     */
    public function baseChatComplete(ChatCompleteEntity $entity): array
    {
        $key = config('chatgpt.qinghua_api_key');
        $headers = [
            'Accept: application/json',
            'Content-Type: application/json',
            "Authorization:  Bearer $key"
        ];
        $postData = [
            'model' => $entity->model_sign,
            'prompt' => $entity->prompt,
            'temperature' => $entity->temperature,
            'max_tokens' => $entity->max_output_tokens,
            'stream' => false,
            'user_id' => $entity->user_id,
            'tools' => [
                [
                    'type' => 'web_search',
                    'web_search' => [
                        'enable' => true,
                        'search_result' => true,
                    ]
                ]
            ]
        ];
        $url = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        $output = curl_exec($ch);
        curl_close($ch);
        return json_decode($output, true);
    }


    private function streamChatCompleteV2(ChatCompleteEntity $entity): array
    {
        header('Content-type: text/event-stream');
        header('Cache-Control: no-cache');
        $postData = [
            'model' => $entity->model_sign,
            'messages' => $this->messageBuild($entity),
            'stream' => true,
            'temperature' => $entity->temperature,
            'max_tokens' => $entity->max_output_tokens,
            'user_id' => $entity->user_id,
            'tools' => [
                [
                    'type' => 'web_search',
                    'web_search' => [
                        'enable' => true,
                        'search_result' => true,
                    ]
                ]
            ]
        ];
        $key = config('chatgpt.qinghua_api_key');
        $headers = [
            'Accept: text/event-stream',
            'Content-Type: application/json',
            "Authorization:  Bearer $key"
        ];
        $url = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
        $isEcho  = $entity->stream;
        $callback = function ($ch, $data) use ($isEcho) {
            $pattern = '/data: (\{.*?\})\n/s';
            $donePattern = '/data: \[\s*DONE\s*\]/s'; // 匹配 "data: [DONE]" 的模式
            $offset = 0;
            // 检查是否完成
            if (preg_match($donePattern, $data)) {
                $eventId = $this->getCurrentMilliseconds();
                if ($isEcho) {
                    echo "id: $eventId\n";
                    echo "data: [DONE]\n\n";
                }
                return strlen($data); // 返回数据长度以避免写入错误
            }

            // 循环处理所有匹配项
            while (preg_match($pattern, $data, $matches, PREG_OFFSET_CAPTURE, $offset) > 0) {
                $json = $matches[1][0]; // 获取匹配的 JSON 字符串
                $offset = $matches[0][1] + strlen($matches[0][0]); // 更新偏移量以处理下一个匹配项
                $decoded = json_decode($json, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    // 处理解析后的结果
                    $eventId = $this->getCurrentMilliseconds();
                    $dataText = $decoded['choices'][0]['delta']['content'];
                    // 判断是否有换行符，换行符需要转义
                    if (strpos($dataText, "\n") !== false) {
                        $dataText = str_replace("\n", "\\n", $dataText);
                    }
                    if ($isEcho) {
                        echo "id: $eventId\n";
                        echo 'data: ' . $dataText . "\n\n";
                    }
                    $this->content .= $dataText;
                } else {
                    // 处理 JSON 解析错误
                    LogError('zhipu', '消息解析异常', '', [
                        'data' => $matches[1][0],
                        'error' => json_last_error(),
                        'message' => json_last_error_msg()
                    ]);
                }
                try {
                    ob_flush();
                    flush();
                } catch (\Exception $exception) {
                }
            }
            return strlen($data);
        };


        $ch = curl_init();
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, $callback);
        curl_exec($ch);

        return [
            'role' => 'assistant',
            'content' => $this->content
        ];
    }


    /**
     * 智谱对话message格式组装
     * @param ChatCompleteEntity $entity
     * @return array
     */
    private function messageBuild(ChatCompleteEntity $entity)
    {
        $buildMessage = [];
        foreach ($entity->prompt as $message) {
            $content = $message['content'];
            switch ($message['type'] ?? 'text') {
                case 'text':
                    $content = $message['content'];
                    break;
                case 'img':
                    $imgMsg = [];
                    foreach ($message['imgs'] as $img) {
                        $imgMsg[] = [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => $img
                            ]
                        ];
                    }
                    $imgMsg[] = [
                        'type' => 'text',
                        'text' => $message['content']
                    ];
                    $content = $imgMsg;
                    break;
                case 'video':
                    $videoMsg = [];
                    foreach ($message['videos'] as $video) {
                        $videoMsg[] = [
                            'type' => 'video_url',
                            'video_url' => [
                                'url' => $video
                            ]
                        ];
                    }
                    $videoMsg[] = [
                        'type' => 'text',
                        'text' => $message['content']
                    ];
                    $content = $videoMsg;
                    break;
            }

            $buildMessage[] = [
                'role' => $message['role'],
                'content' => $content
            ];
        }
        return $buildMessage;
    }
}
