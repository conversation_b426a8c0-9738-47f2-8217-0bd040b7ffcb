<?php

/**
 * @author: xuz<PERSON><PERSON><PERSON>
 * @Time: 2023/9/28   20:59
 */

namespace app\libraries\service\ai_open\ai_person_video\lmp;

use app\constdir\SysErrorCode;
use app\libraries\service\ai_open\ai_person_video\AiPersonVideoAbstract;
use app\libraries\service\ai_open\ai_person_video\entity\Mp3VideoEntity;
use app\libraries\service\ai_open\ai_person_video\entity\TextVideoEntity;
use think\facade\Log;

class HeygenVideo extends AiPersonVideoAbstract
{
    public function createTextVideo(TextVideoEntity $entity): array
    {
        //视频脚本和声音不能同时为空
        if (!$entity->videoText && !$entity->voiceId) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '视频脚本和声音不能同时为空');
        }
        $faceId = false;
        $avtarId = false;
        if ($entity->avatarId === '' && $entity->peronUrl) {
            //对图片进行抠图处理
            $removeBg = $this->removeBg($entity->peronUrl);
            if ($removeBg) {
                $fileData = $removeBg;
            } else {
                $filePath = parse_url($entity->peronUrl, PHP_URL_PATH);
                //去除左边多余的斜杠
                $filePath = ltrim($filePath, '/');
                $filePath = public_path() .  $filePath;
                $fileData = file_get_contents($filePath);
            }
            $faceId = $this->uploadTalkingPhoto($fileData);
            if (!$faceId) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '上传数字人头像失败');
            }
        } else {
            $avtarId = $entity->avatarId;
        }

        $offset = [
            'x' => $entity->headX,
            'y' => $entity->headY
        ];
        $clips = [
            'avatar_style' => 'normal',
            'caption' => false,
            'offset' => $offset,
            'scale' => $entity->headScale ?? 1,
        ];
        if (empty($entity->videoText)) {
            $clips['input_audio'] = $entity->soundWord;
        } else {
            $clips['input_text'] = $entity->videoText;
            $clips['voice_id'] = $entity->voiceId;
        }
        if ($faceId) {
            $clips['talking_photo_style'] = 'normal';
            $clips['talking_photo_id'] = $faceId;
        } else {
            $clips['avatar_id'] = $avtarId;
            $clips['avatar_style'] = 'normal';
        }
        // 判断是否为定制模型
        $custonAvatarId = [
            'accde4f048a44024a03a09df3485aa59',
            'df4bd29c2c9843fc97d06126f9b05a28',
            '8f155122e2744a8e8376e3c8db5ebbd6',
            'bbe4ed291dd7409aac122d53f9829918'
        ];
        if (!empty($entity->avatarId) && in_array($entity->avatarId, $custonAvatarId)) {
            $v2data = $this->buildV2Data($entity);
            $result = $this->createVideoV2($v2data);
            $result = (array) json_decode($result, true);
        } else {
            $result = $this->createVideo($clips, $entity->background, $entity->ratio);
        }
        if (!$result) {
            throw new \Exception('创作异常请稍后重试');
        }
        if (empty($result['data']['video_id'])) {
            LogError('heygen', 'heygen视频创作失败', 'heygen视频创作失败', ['ret' => $result]);
            throw new \Exception('创作失败请稍后重试');
        }
        return [
            'id' => $result['data']['video_id'],
            'status' => 'success',
            'video_url' => $result['data']['video_url'] ?? ''
        ];
    }


    /**
     * 扣除背景
     * @param $imgUrl
     * @return false|string
     */
    public function removeBg($imgUrl)
    {
        LogInfo('heygen', '开始扣除背景', 'heygen扣除背景参数', ['imgUrl' => $imgUrl]);
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, 'https://techsz.aoscdn.com/api/tasks/visual/segmentation');
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "X-API-KEY: wxtk8u80lf7hod7ow",
            "Content-Type: multipart/form-data",
        ));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_POSTFIELDS, array('sync' => 1, 'image_url' => $imgUrl));
        $response = curl_exec($curl);
        $result = curl_errno($curl) ? curl_error($curl) : $response;
        curl_close($curl);
        $result = json_decode($result, true);
        if (!$result || empty($result['data']['image'])) {
            LogError('heygen', '扣除背景失败', 'heygen扣除背景失败', ['ret' => $result]);
            return '';
        }
        return @file_get_contents($result['data']['image']);
    }

    public function createVideo($clips, $background, $ratio)
    {
        LogInfo('heygen', '开始创作', 'heygen视频创作参数,开始创作', ['clips' => $clips, 'background' => $background, 'ratio' => $ratio]);
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => "https://api.heygen.com/v1/video.generate",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 1000,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode([
                'background' => $background,
                'ratio' => $ratio,
                'test' => false,
                'version' => 'v1alpha',
                'clips' => [
                    $clips
                ]
            ]),
            CURLOPT_HTTPHEADER => [
                "accept: application/json",
                "content-type: application/json",
                "x-api-key: " . config('chatgpt.heygen_api_key')
            ],
            // CURLOPT_PROXY => "https://us-pr.oxylabs.io",
            // CURLOPT_PROXYPORT => "10001",
        ]);

        $response = curl_exec($curl);
        LogInfo('heygen', '创作结果', 'heygen视频创作结果', ['ret' => $response]);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            LogError('heygen', '创作异常', 'heygen视频创作异常', ['ret' => $err]);
            throw new \Exception('创作异常请稍后重试');
        } else {
            return json_decode($response, true);
        }
    }

    public function createVideoV2($data)
    {
        LogInfo('heygen', '开始创作', 'heygen视频创作参数,开始创作', ['data' => $data]);
        $client = new \GuzzleHttp\Client();
        $response = $client->request('POST', 'https://api.heygen.com/v2/video/generate', [
            'body' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'headers' => [
                'accept' => 'application/json',
                'content-type' => 'application/json',
                'x-api-key' => config('chatgpt.heygen_api_key'),
            ],
        ]);
        $result = $response->getBody();
        LogInfo('heygen', '创作结果', 'heygen视频创作结果', ['ret' => $result]);
        return $result;
    }

    public function createMp3Video(Mp3VideoEntity $entity): array
    {
        // TODO: Implement createMp3Video() method.
        return [];
    }

    /**
     * 查询数字人视频生成结果
     * @param string $videoId
     * @param $queryCount
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getVideoResult(string $videoId, $queryCount = 0): array
    {
        LogInfo('heygen', '查询结果开始', '查询结果开始', ['id' => $videoId]);
        // var_dump('查询id：' . $videoId);
        if ($queryCount > 10) {
            throw new \Exception('获取创作结果失败，请稍后重试');
        }
        $client = new \GuzzleHttp\Client();

        $response = $client->request('GET', 'https://api.heygen.com/v1/video_status.get?video_id=' . $videoId, [
            'headers' => [
                'accept' => 'application/json',
                'x-api-key' => config('chatgpt.heygen_api_key'),
            ],
            'timeout' => 1800, //超时时间
            // 'proxy' => 'https://us-pr.oxylabs.io:10001'
        ]);

        $res = $response->getBody();
        LogInfo('heygen', '查询结果', '获得查询结果', ['body' => $res]);
        $res = json_decode($res, true);
        LogInfo('heygen', '查询结果', 'decode的查询结果', ['body' => $res]);
        if (!$res) {
            sleep(10);
            $queryCount++;
            return $this->getVideoResult($videoId, $queryCount);
        }
        if ($res['data']['status'] == 'failed') {
            throw new \Exception('获取创作结果失败，请稍后重试');
        }
        if ($res['data']['status'] != 'completed') {
            sleep(10);
            $queryCount++;
            return $this->getVideoResult($videoId, $queryCount);
        }
        LogInfo('heygen', '查询结果', '成功查询到了结果', ['ret' => $res['data']['video_url'] ?? '']);
        return [
            'status' => 'success',
            'url' => $res['data']['video_url'] ?? ''
        ];
    }

    /**
     * 创建数字人头像信息
     * @param  $data
     * @return string
     * @throws \app\libraries\exception\ApiException
     */
    private function uploadTalkingPhoto($data): string
    {
        $url = "https://upload.heygen.com/v1/talking_photo";

        $headers = array(
            "Content-Type: image/jpeg",
            "x-api-key: " . config('chatgpt.heygen_api_key'),
        );

        $ch = curl_init();
        //通过路径获取文件流信息

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // curl_setopt($ch, CURLOPT_PROXY, 'https://us-pr.oxylabs.io');
        // curl_setopt($ch, CURLOPT_PROXYPORT, 10001);
        curl_close($ch);

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            throwException('Request Error:' . curl_error($ch));
        } else {
            $res = json_decode($response, true);
            return $res['data']['talking_photo_id'] ?? '';
        }
        return '';
    }

    public function getPortraitList(): array
    {
        //从缓存获取
        $cacheKey = 'heygen_portrait_list';
        if (cache($cacheKey)) {
            return cache($cacheKey);
        }
        $client = new \GuzzleHttp\Client();

        $response = $client->request('GET', 'https://api.heygen.com/v2/avatars', [
            'headers' => [
                'accept' => 'application/json',
                'x-api-key' => config('chatgpt.heygen_api_key'),
            ],
            'timeout' => 1800, //超时时间
            // 'proxy' => 'https://us-pr.oxylabs.io:10001'
        ]);

        $res = $response->getBody();
        $res = json_decode($res, true);
        if (!$res) {
            throw new \Exception('获取数字人头像失败');
        }
        $portraitList = [];
        foreach ($res['data']['avatars'] as $item) {
            $portraitList[] = [
                'avatar_id' => $item['avatar_id'],
                'img_url' => $item['preview_image_url'],
                'video_url' => $item['preview_video_url'],
            ];
        }
        //缓存30天
        cache($cacheKey, $portraitList, 60 * 60 * 24 * 30);
        return $portraitList;
    }

    public function v2Data()
    {
        # https://docs.heygen.com/reference/create-an-avatar-video-v2
        $bodyData = [
            "test" => false,  // 是否开启测试模式
            "caption" => true,  // 是否开启文档标题
            "title" => "测试视频",  // 视频标题
            "video_inputs" => [
                [
                    "character" => [
                        "type" => "avatar", // 两种设置模式：图片上传和系统头像，目前示例为系统头像设置，具体查看文档AvatarSettings和TalkingPhotoSettings
                        "avatar_id" => "accde4f048a44024a03a09df3485aa59", // 头像id
                        "scale" => 1.0, // 头像缩放比例，取值范围为0到2.0。默认为1.0。
                        "avatar_style" => "normal", // 头像风格，circle, normal, closeUp
                        "offset" => [  // 头像偏移量
                            "x" => 0.3,
                            "y" => 0.3
                        ],
                        "matting" => false, // 是否进行抠图
                        "circle_background_color" => '', // 使用圆形风格时，圆形背景的颜色。
                    ],
                    "voice" => [
                        "type" => "text", //
                        "voice_id" => "91526b1eb59544d89325bc70318e2896",
                        "input_text" => "大家好，我是十八般小艺的创始人的数字分身，很高兴与大家见面了！我们的数字分身除了强大的AI加持外还能训练专属的个人知识库，赶快来加入我们吧！",
                    ],
                    "background" => [
                        "type" => "image", // 背景类型,color、image、video，这里是color类型
                        "url" => "https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/0011b9119123450ba7a2503d60bfd0f3.jpg",
                    ]
                ]
            ],
            "dimension" => [
                "width" => 1080, // 视频宽度
                "height" => 1920, // 视频高度
            ]
        ];
        return $bodyData;
    }

    public function buildV2Data(TextVideoEntity $entity)
    {
        $bodyData = [
            'test' => false,
            'caption' => false,
        ];
        // 步骤1：头像信息
        if (!empty($entity->talking_photo_id)) {  //上传图片模式
            $bodyData['video_inputs'][0]['character'] = [
                'type' => 'talking_photo',
                'talking_photo_id' => $entity->talking_photo_id,
                'scale' => $entity->headScale ?? 1,
                'offset' => [
                    'x' => $entity->headX,
                    'y' => $entity->headY
                ],
                'talking_style' => 'stable',
                'expression' => 'happy'
            ];
        } elseif (!empty($entity->avatarId)) {  //系统头像模式
            $bodyData['video_inputs'][0]['character'] = [
                'type' => 'avatar',
                'avatar_id' => $entity->avatarId,
                'scale' => $entity->headScale ?? 1,
                'offset' => [
                    'x' => $entity->headX,
                    'y' => $entity->headY
                ],
                'avatar_style' => 'normal',
                'matting' => false,
            ];
        } else {
            throwException(SysErrorCode::SYS_ERROR_CODE, '头像信息错误');
        }
        // 步骤2： 视频内容信息
        if (!empty($entity->videoText)) { // 文本模式
            $bodyData['video_inputs'][0]['voice'] = [
                'type' => 'text',
                'voice_id' => $entity->voiceId,
                'input_text' => $entity->videoText,
            ];
        } elseif (!empty($entity->soundWord)) { // 音频模式
            $bodyData['video_inputs'][0]['voice'] = [
                'type' => 'audio',
                'audio_url' => $entity->soundWord,
            ];
        } else {
            throwException(SysErrorCode::SYS_ERROR_CODE, '视频内容信息错误');
        }

        // 步骤3：背景信息
        $background = $entity->background;
        if (empty($background)) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '背景信息错误');
        }
        // 如果开头以#开头，代表16进制的颜色背景
        if (strpos($background, '#') === 0) {
            $bodyData['video_inputs'][0]['background'] = [
                'type' => 'color',
                'color' => $background,
            ];
        // 如果以http或者https开头，并且结尾是视频格式，代表视频背景
        } elseif (preg_match('/(http|https):\/\/.*\.(mp4|avi|flv|wmv|mov|3gp|rmvb|rm|mkv|webm)$/i', $background)) {
            $bodyData['video_inputs'][0]['background'] = [
                'type' => 'video',
                'url' => $background,
                'play_style' => 'once'
            ];
        } else {
            $bodyData['video_inputs'][0]['background'] = [
                'type' => 'image',
                'url' => $background,
            ];
        }
        // 步骤4：视频是横屏还是竖屏
        $ratio = $entity->ratio;
        if ($ratio == '9:16') {
            $bodyData['dimension'] = [
                'width' => 1080,
                'height' => 1920,
            ];
        } elseif ($ratio == '16:9') {
            $bodyData['dimension'] = [
                'width' => 1920,
                'height' => 1080,
            ];
        } else {
            throwException(SysErrorCode::SYS_ERROR_CODE, '视频比例错误');
        }
        return $bodyData;
    }
}
