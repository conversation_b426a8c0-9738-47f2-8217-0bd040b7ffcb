<?php

namespace app\libraries\service\cos;

use app\constdir\SysTime;
use app\libraries\service\cos\entity\ConfigEntity;
use app\libraries\utils\ImgUtil;
use app\libraries\utils\traits\Signleton;
use app\libraries\utils\Util;
use TencentCloud\Common\Credential;
use TencentCloud\Sts\V20180813\Models\GetFederationTokenRequest;
use TencentCloud\Sts\V20180813\StsClient;
use Throwable;

class CosStsService
{
    use Signleton;

    protected StsClient $client;

    protected ConfigEntity $configEntity;

    public function __construct($config = [])
    {
        $config = array_merge_depth($config, config('resource.cos'));
        $this->configEntity = new ConfigEntity();
        $this->configEntity->baseSet($config, true);
        if (empty($this->configEntity->host)) {
            $this->configEntity->host = 'https://' . $this->configEntity->bucket . '.cos.' . $this->configEntity->region . '.myqcloud.com';
        }

        $cred = new Credential($this->configEntity->secretId, $this->configEntity->secretKey);
        $this->client = new StsClient($cred, $this->configEntity->region);
    }

    /**
     * 获取地址
     * @param $url
     * @return string
     */
    public function getUrl($url): string
    {
        return ImgUtil::completeUrl($this->configEntity->host, $url);
    }

    /**
     * remove地址
     * @param $url
     * @return string
     */
    public function removeDomain($url): string
    {
        return ImgUtil::removeDomain($this->configEntity->host, $url);
    }

    /**
     * 生成的临时凭据-上传
     * @param string $prefixPath
     * @param int $expire
     * @return array
     */
    public function getFederationTokenByPut(string $prefixPath, int $expire = SysTime::FIVEMINUTES): array
    {
        $prefixPath = trim($prefixPath, '/');
        $name = 'cos_sts_post';
//        $key = "{$name}_{$prefixPath}";
//        $res = CosStsCache::getInstance()->getItem($key);
//        if (!empty($res)) {
//            return $res;
//        }

        $statement = [
            [
                'principal' => [
                    'qcs' => [
                        'qcs::cam::uid/' . $this->configEntity->appId . ':uin/' . $this->configEntity->sunAccount,
                    ],
                ],
                'effect' => 'allow',
                'action' => [
                    'name/cos:PostObject',
                    'name/cos:PutObject',
                    'name/cos:GetObject',
                ],
                'resource' => [
                    'qcs::cos:' . $this->configEntity->region . ':uid/' . $this->configEntity->appId . ':' . $this->configEntity->bucket . '/' . $prefixPath . '/*',
                ],
            ]
        ];
        $res = $this->getFederationToken($name, $statement, $expire);

        return [
            'token' => $res['Credentials']['Token'] ?? '',
            'secretId' => $res['Credentials']['TmpSecretId'] ?? '',
            'secretKey' => $res['Credentials']['TmpSecretKey'] ?? '',
            'expiredTime' => (int)($res['ExpiredTime'] ?? 0),
            'nowTime' => time(),
            'bucket' => $this->configEntity->bucket,
            'region' => $this->configEntity->region,
            'prefixPath' => $prefixPath,
            'host' => $this->configEntity->host,
        ];

//        CosStsCache::getInstance()->setItem($key, $res);
//        return $res;
    }

    /**
     * 生成的临时凭据
     * @param string $name
     * @param array $statement
     * @param int|null $durationSeconds
     * @return array
     */
    public function getFederationToken(string $name, array $statement, int $durationSeconds = null): array
    {
        try {
            $data = [
                'Name' => $name,
                'Policy' => json_encode([
                    'version' => '2.0',
                    'statement' => $statement,
                ]),
                'DurationSeconds' => $durationSeconds,
            ];

            LogInfo('cos.sts.getFederationToken', '腾讯云对象存储-sts', '请求参数', $data);

            $req = new GetFederationTokenRequest();
            $req->deserialize($data);
            $res = $this->client->GetFederationToken($req);
            $res = json_decode($res->toJsonString(), true);

            LogInfo('cos.sts.getFederationToken', '腾讯云对象存储-sts', '返回值', $res);

            return $res;
        } catch (Throwable $e) {
            LogError('cos.sts.getFederationToken', '腾讯云对象存储-sts', '异常', [
                'arg' => func_get_args(),
                'error' => Util::normalizeException($e),
            ]);
        }
        return [];
    }
}