<?php
/*
 * @Author: 杨红兵 
 * @Date: 2022-08-16 12:07:37 
 * @Last Modified by: 杨红兵
 * @Last Modified time: 2022-08-16 12:10:04
 */

namespace app\libraries\service\map\abstract;

use app\libraries\utils\UrlUtil;

abstract class FormatAbstract
{
    public function formatUrl(string $path, ...$params): string
    {
        array_push($params, config('map.key'));
        return UrlUtil::formatUrl(config('map.host'), $path, ...$params);
    }
}
