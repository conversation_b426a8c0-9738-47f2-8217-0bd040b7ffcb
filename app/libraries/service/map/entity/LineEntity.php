<?php
/*
 * @Author: 杨红兵 
 * @Date: 2022-08-16 13:45:08 
 * @Last Modified by: 杨红兵
 * @Last Modified time: 2022-08-17 15:04:34
 */

namespace app\libraries\service\map\entity;

use app\libraries\utils\entity\BaseEntity;

class LineEntity extends BaseEntity
{
    /**
     * 标识-起点
     *
     * @var mixed
     * <AUTHOR>
     * @DateTime 2022-08-17 14:54:30
     *
     */

    public $identificationStart = 0;

    /**
     * 标识-终点
     *
     * @var mixed
     * <AUTHOR>
     * @DateTime 2022-08-17 14:54:30
     *
     */

    public $identificationEnd = 0;

    /**
     * 距离 单位 米
     *
     * @var integer
     * <AUTHOR>
     * @DateTime 2022-08-16 13:46:06
     *
     */
    public int $distance = 0;

    /**
     * 预计到达使用时间
     *
     * @var integer
     * <AUTHOR>
     * @DateTime 2022-08-16 13:46:30
     *
     */
    public int $duration = 0;

    /**
     * 预估车费,单位（分）
     * @var int
     */
    public int $fee = 0;
}
