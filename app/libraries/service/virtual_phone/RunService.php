<?php
/**
 * @Author: zhangcc
 * @Date: 2022/11/24
 */

namespace app\libraries\service\virtual_phone;

use app\constdir\SysTime;
use app\libraries\models\VirtualPhoneBindModel;
use app\libraries\utils\EncryptUtil;
use app\libraries\utils\traits\Signleton;
use app\orders\cache\VirtualPhoneCache;
use app\orders\constdir\MiddleNumberConst;

class RunService
{
    use Signleton;

    /**
     * 两个号码绑定
     * @param  string  $mobileOne
     * @param  string  $mobileTwo
     * @return string
     */
    public function doubleMobile(string $mobileOne, string $mobileTwo): string
    {
        // 获取用户绑定的虚拟号
        $middleNumberOne = VirtualPhoneCache::getInstance()->getMobileBind($mobileOne);
        // 获取技师绑定的虚拟号
        $middleNumberTwo = VirtualPhoneCache::getInstance()->getMobileBind($mobileTwo);

        // 有效虚拟号
        $validMiddleNumber = array_intersect($middleNumberOne, $middleNumberTwo);
        // 存在有效虚拟号
        if (!empty($validMiddleNumber)) {
            return strval(array_values($validMiddleNumber)[0]);
        }

        // 无效虚拟号
        $invalidMiddleNumber = array_merge($middleNumberOne, $middleNumberTwo);
        // 获取有效虚拟号
        $validMiddleNumberNew = array_diff(MiddleNumberConst::YUN_XIN, $invalidMiddleNumber);
        if (empty($validMiddleNumberNew)) {
            return '';
        }

        $validMiddleNumberOne = array_values($validMiddleNumberNew)[0];
        // 绑定虚拟号
        $res = YunXinService::getInstance()->bindCode($validMiddleNumberOne, $mobileOne, $mobileTwo);
        if (empty($res)) {
            return '';
        }

        // 写入缓存
        $expire = time() + SysTime::TWENTY_FIVE_MINUTES;
        // 电话1
        VirtualPhoneCache::getInstance()->setMobileBind(
            $mobileOne,
            $validMiddleNumberOne,
            $expire,
            SysTime::TWENTY_NINE_MINUTES
        );
        // 电话2
        VirtualPhoneCache::getInstance()->setMobileBind(
            $mobileTwo,
            $validMiddleNumberOne,
            $expire,
            SysTime::TWENTY_NINE_MINUTES
        );

        // 生成绑定关系
        list($encryptionMobileOne, $mobileSecretKeyOne) = EncryptUtil::encryptionNumberidentification($mobileOne);
        list($encryptionMobileTwo, $mobileSecretKeyTwo) = EncryptUtil::encryptionNumberidentification($mobileTwo);
        $virtualPhoneBindModel = (new VirtualPhoneBindModel);
        $virtualPhoneBindModel->mobileOne = $encryptionMobileOne;
        $virtualPhoneBindModel->mobileSecretKeyOne = $mobileSecretKeyOne;
        $virtualPhoneBindModel->mobileTwo = $encryptionMobileTwo;
        $virtualPhoneBindModel->mobileSecretKeyTwo = $mobileSecretKeyTwo;
        $virtualPhoneBindModel->bindId = $res['bindId'];
        $virtualPhoneBindModel->middleNumber = $res['middleNumber'];
        $virtualPhoneBindModel->expireTime = $expire;
        $virtualPhoneBindModel->save();

        return strval($validMiddleNumberOne);
    }
}