<?php

/**
 * @author: xuzhengyang
 * @Time: 2023/4/2   16:29
 */

namespace app\libraries\service\wxpay;

use app\constdir\SysErrorCode;
use app\libraries\exception\ApiException;
use app\merchant\models\MerchantConfigModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use EasyWeChat\Factory;

class WxpayService
{
    private $payAppId = ''; //发起支付的应用id，其中xcx代表小程序端发起，gzh代表公众号发起
    private $mchId = ''; //商户ID
    private $apiKey = ''; //API V2的key

    private $notifyUrl = ''; //回调地址

    private $openId = ''; //用户openId

    /**
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     * @throws ApiException
     */
    public function __construct($merchantGuid, $payEnv = 'xcx', $openId = '', $from = 'default')
    {
        //获取当前商家配置信息
        $configs = MerchantConfigModel::getMerchantConfigs($merchantGuid);
        if (empty($configs['wxpay_mchid'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '支付商户号未配置');
        }
        if (empty($configs['wxpay_api_key'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '支付密钥未配置');
        }

        if ($payEnv == 'xcx') {
            $this->payAppId = $configs['xcx_app_id'] ?? '';
            // 小艺商协通存在共用的情况，如果来自商协通，做特殊处理
            if ($from == 'sxt' && $configs['xcx_app_id'] == 'wx1d5d60eb69be794f') {
                $this->payAppId = 'wxd20b1bc73ce38e63';
            }
        } elseif ($payEnv == 'gzh') {
            $this->payAppId = $configs['gzh_app_id'] ?? '';
        } else {
            throwException(SysErrorCode::SYS_ERROR_CODE, '无效的支付环境');
        }

        if (empty($this->payAppId)) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '支付端appid未配置');
        }
        $this->mchId = $configs['wxpay_mchid'];
        $this->apiKey = $configs['wxpay_api_key'];
        $this->openId = $openId;
    }

    /**
     * 获取支付应用
     * @return \EasyWeChat\Payment\Application
     */
    public function getWxPayApp()
    {
        $config = [
            // 必要配置
            'app_id'             => $this->payAppId,
            'mch_id'             => $this->mchId,
            'key'                => $this->apiKey,   // API v2 密钥 (注意: 是v2密钥 是v2密钥 是v2密钥)

            // 如需使用敏感接口（如退款、发送红包等）需要配置 API 证书路径(登录商户平台下载 API 证书)
            'cert_path'          => '', // XXX: 绝对路径！！！！
            'key_path'           => '',      // XXX: 绝对路径！！！！

            'notify_url'         => '',     // 你也可以在下单时单独设置来想覆盖它
        ];


        return Factory::payment($config);
    }

    /**
     * 发起微信支付
     * @param $payDes
     * @param $amount
     * @param $orderNo
     * @param $notifyUrl
     * @param $openId
     * @return array|string
     * @throws ApiException
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function doWxPay($payDes, $amount, $orderNo, $notifyUrl)
    {
        $app = $this->getWxPayApp();
        $payData = [
            'body' => $payDes,
            'out_trade_no' => $orderNo,
            'total_fee' => yuan_to_fen($amount),
            'notify_url' => $notifyUrl, // 支付结果通知网址，如果不设置则会使用配置里的默认地址
            'trade_type' => 'JSAPI', // 请对应换成你的支付方式对应的值类型
            'openid' => $this->openId,
            'sign_type'    => 'MD5',
        ];
        $result = $app->order->unify($payData);
        if (empty($result['return_code']) || empty($result['prepay_id'])) {
            LogWarning('orderPay', '数字人充值支付失败', '拉起失败', $result);
            throwException(SysErrorCode::SYS_ERROR_CODE, '支付异常请您重试');
        }
        $jssdk = $app->jssdk;
        $payInfo = $jssdk->bridgeConfig($result['prepay_id'], false); // 返回数组
        $payInfo['orderNo'] = $orderNo;

        return $payInfo;
    }


    public function doGzhWxPay($payDes, $amount, $orderNo, $notifyUrl, $openId)
    {
    }

    public function queryPayInfo($orderNo)
    {
        $app = $this->getWxPayApp();
        $result =  $app->order->queryByOutTradeNumber($orderNo);
        if (!empty($result['trade_state']) && $result['trade_state'] == 'SUCCESS') {
            return [
               'isPay' => true,
               'transactionId' => $result['transaction_id']
            ];
        }

        return [
            'isPay' => false,
            'transactionId' => ''
        ];
    }
}
