<?php
/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @Time: 2023/3/25   22:55
 */


namespace app\redeem\cache;

use app\libraries\utils\cache\BaseRedisCache;

use app\libraries\utils\traits\Signleton;

class RedeemCache extends BaseRedisCache
{
    use Signleton;

    const DO_REDEEM_USE = 'do_redeem_use_';

    /**
     * 兑换加锁
     * @param $uid
     * @return bool|string
     */
    public function redeemUseLock($uid)
    {
        $key = self::DO_REDEEM_USE . $uid;
        return $this->lock($key, 2);
    }

    public function unlockRedeemUse($uid,$value)
    {
        $key = self::DO_REDEEM_USE . $uid;
        return $this->unLock($key, $value);
    }

}