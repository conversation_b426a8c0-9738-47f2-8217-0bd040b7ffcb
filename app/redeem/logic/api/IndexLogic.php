<?php
/**
 * @author: xuzhengyang
 * @Time: 2023/3/25   22:53
 */


namespace app\redeem\logic\api;

use app\constdir\SysErrorCode;
use app\libraries\exception\ApiException;
use app\libraries\service\token\TokenService;
use app\redeem\cache\RedeemCache;
use app\redeem\models\RedeemCodeBuyGoodsModel;
use app\redeem\models\RedeemCodesModel;
use app\redeem\models\RedeemCodeUserRecordModel;
use app\user\models\UserAssetsModel;

class IndexLogic
{

    public function buyGoods()
    {
        $list = RedeemCodeBuyGoodsModel::getInstance()->select()->toArray();
        foreach ($list as &$value) {
            $value['goodsPrice'] = fen_to_yuan($value['goodsPrice']);
        }
        return $list;
    }

    /**
     * 使用兑换码兑换
     * @param $data
     * @return array|void
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function useCode($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $isLock = RedeemCache::getInstance()->redeemUseLock($userId);
        if (!$isLock) {
            throwException(SysErrorCode::LOCK_ERROR);
        }
        //查询兑换码信息
        $codeInfo = RedeemCodesModel::getInstance()->where('code_text', $data['redeemCode'])->findOrEmpty();
        if ($codeInfo->isEmpty()) {
            throwException(SysErrorCode::QUERY_EMPTY, '无效的兑换码');
        }
        //兑换码时效性
        $nowTime = time();
        if ($nowTime < $codeInfo['codeBeginTime']) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '当前兑换码未生效');
        }
        if ($nowTime > $codeInfo['codeEndTime'] || $codeInfo['status'] == RedeemCodesModel::CODE_STATUS_INVALID) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '兑换码已过期');
        }
        //单次兑换码使用状态验证
        if ($codeInfo['codeType'] == RedeemCodesModel::CODE_TYPE_ONCE && $codeInfo['status'] == RedeemCodesModel::CODE_STATUS_USED) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '当前兑换码已使用');
        }
        //兑换码兑换记录验证
        $codeUseRecord = RedeemCodeUserRecordModel::getInstance()
            ->where('code_guid', $codeInfo['guid'])
            ->where('platform_user_sys_id', $userId)
            ->findOrEmpty();
        if (!$codeUseRecord->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '当前兑换码您已兑换，请不要重复操作');
        }
        //执行兑换
        RedeemCodesModel::getInstance()->startTrans();
        try {
            //兑换码状态更新
            RedeemCodesModel::getInstance()
                ->where('sys_id', $codeInfo['sysId'])
                ->update([
                    'status' => RedeemCodesModel::CODE_STATUS_USED
                ]);
            //兑换记录
            $recordModel = new RedeemCodeUserRecordModel();
            $recordModel->codeGuid = $codeInfo['guid'];
            $recordModel->platformUserSysId = $userId;
            $recordModel->save();

            //增加聊天点数
            UserAssetsModel::getInstance()
                ->where('platform_user_sys_id', $userId)
                ->inc('chat_count', $codeInfo['codeChatCount'])
                ->update();
            RedeemCache::getInstance()->unlockRedeemUse($userId, $isLock);
            RedeemCodesModel::getInstance()->commit();
            return [];
        } catch (ApiException $exception) {
            RedeemCodesModel::getInstance()->rollback();
            throwException(SysErrorCode::SYS_ERROR_CODE, $exception->getMessage());
        } catch (\Throwable $exception) {
            RedeemCodesModel::getInstance()->rollback();
            var_dump($exception->getMessage());
            throwException(SysErrorCode::SYS_ERROR_CODE, '操作异常请重试');
        } finally {
            RedeemCache::getInstance()->unlockRedeemUse($userId, $isLock);
        }


    }

}