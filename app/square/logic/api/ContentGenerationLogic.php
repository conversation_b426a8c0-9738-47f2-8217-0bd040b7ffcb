<?php

/**
 * @author: xuzhengyang
 * @Time: 2023/7/5   14:44
 */

namespace app\square\logic\api;

use app\constdir\SysErrorCode;
use app\libraries\service\token\TokenService;
use app\square\models\CopywritingCategoryModel;
use app\square\models\UserCopywritingCategoryModel;
use app\user\models\UsersModel;

class ContentGenerationLogic
{
    /**
     * 分类列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function copywritingCategory($data)
    {
        $merchantGuid = $data['merchantGuid'] ?? '';
        if (!$merchantGuid) {
           $merchantGuid = request()->header('merchantGuid');
        }
        return CopywritingCategoryModel::getInstance()
            ->where('pid', 0)
            ->where('status', CopywritingCategoryModel::STATUS_NORMAL)
            ->where('cate_type', $data['cateType'] ?? '')
            ->where('merchant_guid', $merchantGuid)
            ->field('id,title')
            ->order('sort')
            ->select()
            ->toArray();
    }

    /**
     * 分类下的场景内容列表
     * @param $data
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function categoryConentList($data)
    {
        //获取用户已收藏列表
        $uid = TokenService::getInstance()->getTokenEntity()->userId;
        $cateList = [];
        $collectIds = [];
        if ($uid) {
            $collectIds = UserCopywritingCategoryModel::getInstance()
                ->where('platform_user_sys_id', $uid)
                ->column('copywriting_category_id');
        }
        $cateList = CopywritingCategoryModel::getInstance()
            ->where('pid', $data['cateId'])
            ->where('status', CopywritingCategoryModel::STATUS_NORMAL)
            ->field('id,title,image,desc')
            ->order('sort')
            ->select()
            ->toArray();
        foreach ($cateList as $key => $value) {
            if (in_array($value['id'], $collectIds)) {
                $cateList[$key]['isCollect'] = true;
            } else {
                $cateList[$key]['isCollect'] = false;
            }
        }
        return $cateList;
    }

    /**
     * 场景详情
     * @param $data
     * @return CopywritingCategoryModel|array|mixed|\think\Model
     */
    public function catgoryContentInfo($data)
    {
        return CopywritingCategoryModel::getInstance()
            ->field('id,title,image,desc,pid,cate_type,able_lunci,cate_type,ai_model,cate_type')
            ->where('id', $data['cateId'])
            ->findOrEmpty();
    }

    /**
     * 收藏场景分类
     * @param $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function userCollectionCate($data)
    {
        $uid = TokenService::getInstance()->getTokenEntity()->userId;
        $cateInfo = $this->catgoryContentInfo($data);
        if ($cateInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '场景信息不存在');
        }
        if ($cateInfo->pid == 0) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '无效的场景');
        }
        $existCate = UserCopywritingCategoryModel::getInstance()
            ->where('platform_user_sys_id', $uid)
            ->where('copywriting_category_id', $data['cateId'])
            ->findOrEmpty();
        if (!$existCate->isEmpty()) {
            return [];
        }
        $collectionModel = new UserCopywritingCategoryModel();
        $collectionModel->platformUserSysId = $uid;
        $collectionModel->copywritingCategoryId = $data['cateId'];
        $collectionModel->cateType = $cateInfo['cate_type'];
        $collectionModel->save();
        return [];
    }

    /**
     * 用户收藏的场景列表
     * @param $data
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function userCollectionCateList($data)
    {
        $uid = TokenService::getInstance()->getTokenEntity()->userId;
        if (!$uid) {
            return [];
        }
        $cateList = UserCopywritingCategoryModel::getInstance()
            ->with(['categoryInfo'])
            ->where('platform_user_sys_id', $uid)
            ->where('cate_type', $data['cateType'])
            ->order('sys_id', 'desc')
            ->select()
            ->toArray();
        return $cateList;
    }

    /**
     * 取消场景收藏
     * @param $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function userCancelCate($data)
    {
        $uid = TokenService::getInstance()->getTokenEntity()->userId;
        $cateInfo = $this->catgoryContentInfo($data);
        if ($cateInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '场景信息不存在');
        }
        if ($cateInfo->pid == 0) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '无效的场景');
        }
        $existCate = UserCopywritingCategoryModel::getInstance()
            ->where('platform_user_sys_id', $uid)
            ->where('copywriting_category_id', $data['cateId'])
            ->findOrEmpty();
        if ($existCate->isEmpty()) {
            return [];
        }
        UserCopywritingCategoryModel::destroy(function ($query) use ($existCate) {
            $query->where('sys_id', $existCate['sysId']);
        });

        return [];
    }
}
