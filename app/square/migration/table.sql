CREATE TABLE `chat_content` (
    `sys_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid` char(32) COLLATE utf8mb4_bin NOT NULL COMMENT '唯一关键字段',
    `chat_role` varchar(255) NOT NULL DEFAULT '' COMMENT '聊天角色：user-用户；assistant-机器人',
    `chat_content` TEXT NOT NULL COMMENT '对话内容',
    `msg_id` varchar(255) NOT NULL DEFAULT '' COMMENT '对话msgId',
    `last_msg_id` varchar(255) NOT NULL DEFAULT '' COMMENT '上次兑换msgId',
    `send_day` int unsigned NOT NULL DEFAULT 0  COMMENT '发送日期，格式20230326',
    `platform_user_sys_id` int DEFAULT 0 COMMENT '用户id',
    `create_time` int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time` int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at` int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (`sys_id`),
    KEY `platform_user_sys_id` (`platform_user_sys_id`),
    KEY `send_day` (`send_day`)
)ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='聊天记录';