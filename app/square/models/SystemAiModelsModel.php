<?php

declare(strict_types=1);

namespace app\square\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $modelVendorSign 模型厂商标识
 * @property string $modelName 模型名称
 * @property string $modelSign 模型标识
 * @property string $modelType 模型类型:text-文本；image-图片；video-视频；audio-音频
 * @property string $modelDesc 模型描述
 * @property int $modelStatus 模型状态：0-未启用；1-启用
 * @property int $isMemberFree 会员是否支持免费使用：0-不支持；1-支持
 * @property int $isNetwork 是否支持联网：0-不支持；1-支持
 * @property int $isImageRecognition 是否支持图像识别：0-不支持；1-支持
 * @property int $isVideoRecognition 是否支持视频识别：0-不支持；1-支持
 * @property int $usePrice AI算力点数价格
 * @property int $showOrder 展示顺序
 * @property int $maxOutputTokens 最大返回token数
 * @property int $maxContextWindow 最大上下文窗口
 * @property string $deployAddress 部署地址
 * @property string $deployAddressSecret 部署地址密钥
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class SystemAiModelsModel extends BaseModel
{
    /**
     * @var string
     */
    protected $name = 'system_ai_models';
    protected static bool $isGuid = true;

    // 模型类型
    public const MODEL_TYPE_TEXT = 'text';
    public const MODEL_TYPE_IMAGE = 'image';
    public const MODEL_TYPE_VIDEO = 'video';
    public const MODEL_TYPE_AUDIO = 'audio';

    // 模型状态
    public const MODEL_STATUS_DISABLE = 0;
    public const MODEL_STATUS_ENABLE = 1;

    public const MODEL_VENDOR_LIST = [
        [
            'name' => 'DeepSeek',
            'show_name' => 'DeepSeek',
            'sign' => 'deepseek',
        ],
        [
            'name' => '智谱清言',
            'show_name' => '智谱清言',
            'sign' => 'zhipu',
        ],
        [
            'name' => 'Azure OpenAI',
            'show_name' => '微软AI',
            'sign' => 'azure',
        ],
        [
            'name' => '千帆大模型',
            'show_name' => '文心一言',
            'sign' => 'wenxin',
        ],
        [
            'name' => '星火大模型',
            'show_name' => '讯飞星火',
            'sign' => 'xinghuo',
        ]
    ];

    /**
     * 获取模型标识
     * @param $guid
     * @return array
     */
    public static function getAiModel($guid): array
    {
        $model = self::getInstance()->where('guid', $guid)->findOrEmpty();
        if ($model->isEmpty()) {
            return [];
        }

        return  $model->toArray();
    }
}
