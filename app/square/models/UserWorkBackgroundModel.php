<?php

declare(strict_types=1);

namespace app\square\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property int $platformUserSysId 用户id
 * @property string $backgroundTitle 创作背景标题
 * @property string $backgroundContent 创作背景内容
 * @property int $lastUseTime 最后使用时间
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class UserWorkBackgroundModel extends BaseModel
{
    /**
    * @var string
    */
    protected $name = 'user_work_background';

    protected static bool $isGuid = true;
}
