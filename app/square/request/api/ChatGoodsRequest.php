<?php
/**
 * @author: xuz<PERSON><PERSON><PERSON>
 * @Time: 2023/4/4   20:08
 */


namespace app\square\request\api;

use app\Request;

class ChatGoodsRequest extends Request
{
    protected array $msgs = [];

    protected function getRule(): array
    {
        return  [
            'buy' => [
                'chatGoodsGuid' => 'require'
            ],
            'buyQuery' => [
                'orderNo' => 'require'
            ]
        ];
    }

}