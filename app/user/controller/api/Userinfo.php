<?php

/**
 * @author: xuzhengyang
 * @Time: 2023/3/25   16:57
 */

namespace app\user\controller\api;

use app\user\ApiBaseController;
use app\user\logic\api\UserInfoLogic;
use app\user\request\api\UserInfoRequest;

class Userinfo extends ApiBaseController
{
    protected $middleware = [
        'token' => [
            'except' => ['uploadImg','uploadVideo','uploadVoice','uploadTxt'],
        ],
        'must_login' => [
            'except' => ['uploadImg','index','uploadVideo','uploadVoice','uploadTxt'],
        ],
        // 'bind_mobile' => [],
        'signature' => [
            'except' => ['uploadImg','index','uploadVideo','uploadVoice','uploadTxt'],
        ],
    ];

    /**
     * 用户详情
     * @param UserInfoLogic $logic
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index(UserInfoLogic $logic)
    {
        return $logic->index();
    }

    /**
     * 上传头像图片
     * @param UserInfoLogic $logic
     * @return array
     */
    public function uploadImg(UserInfoLogic $logic)
    {
        return $logic->uploadImg();
    }

    /**
     * 上传视频
     * @param UserInfoLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function uploadVideo(UserInfoLogic $logic)
    {
        return $logic->uploadVideo();
    }

    /**
     * 上传音频
     * @param UserInfoLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function uploadVoice(UserInfoLogic $logic)
    {
        return $logic->uploadVoice();
    }

    public function uploadTxt(UserInfoLogic $logic)
    {
        return $logic->uploadTxt();
    }

    public function uploadFile(UserInfoLogic $logic)
    {
        return $logic->uploadFile();
    }

    /**
     * 修改个人信息
     * @param UserInfoRequest $request
     * @param UserInfoLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function update(UserInfoRequest $request, UserInfoLogic $logic)
    {
        return $logic->update($request->param());
    }

    /**
     * 系统头像列表
     * @param UserInfoLogic $logic
     * @return array
     */
    public function sysHeadImg(UserInfoLogic $logic)
    {
        return $logic->sysHeadImg();
    }

    /**
     * 数字人解绑
     * @param UserInfoLogic $logic
     * @return array
     */
    public function unbind(UserInfoLogic $logic)
    {
        return $logic->unbind();
    }


    /**
     * 获取公众号网页授权地址
     * @param UserInfoRequest $request
     * @param UserInfoLogic $logic
     * @return string
     * @throws \app\libraries\exception\ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function gzhGetRedirectUrl(UserInfoRequest $request, UserInfoLogic $logic)
    {
        return $logic->gzhGetRedirectUrl($request->param());
    }

    /**
     * 公众号授权登录
     * @param UserInfoRequest $request
     * @param UserInfoLogic $logic
     * @return array
     */
    public function gzhCodeLogin(UserInfoRequest $request, UserInfoLogic $logic)
    {
        return $logic->gzhCodeLogin($request->param());
    }

    /**
     * 完成小艺小程序或商协通小程序授权登录
     * @param UserInfoRequest $request
     * @param UserInfoLogic $logic
     * @return array|null
     */
    public function sureXiaoyiCodeLogin(UserInfoRequest $request, UserInfoLogic $logic)
    {
        return $logic->sureXiaoyiCodeLogin($request->param());
    }

    /**
     * 获取页面分享配置
     * @param UserInfoRequest $request
     * @param UserInfoLogic $logic
     * @return array|string[]
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \EasyWeChat\Kernel\Exceptions\RuntimeException
     * @throws \app\libraries\exception\ApiException
     */
    public function getPageShareConfig(UserInfoRequest $request, UserInfoLogic $logic)
    {
        return $logic->getPageShareConfig($request->param());
    }

    /**
     * 绑定AISOP工作台手机号码
     * @param UserInfoRequest $request
     * @param UserInfoLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function buildPcPhone(UserInfoRequest $request, UserInfoLogic $logic)
    {
        return $logic->buildPcPhone($request->param());
    }

    /**
     * 加入创作者联盟
     * @param UserInfoRequest $request
     * @param UserInfoLogic $logic
     * @return array
     */
    public function joinHelpUsers(UserInfoRequest $request, UserInfoLogic $logic)
    {
        return $logic->joinHelpUsers($request->param());
    }

    /**
     * 获取用户加入创作者联盟信息
     * @param UserInfoLogic $logic
     * @return array
     */
    public function helpUserInfo(UserInfoLogic $logic)
    {
        return $logic->helpUserInfo();
    }

    /**
     * 创作者协作数据统计
     * @param UserInfoLogic $logic
     * @return array
     */
    public function helpUserOverview(UserInfoLogic $logic)
    {
        return $logic->helpUserOverview();
    }
}
