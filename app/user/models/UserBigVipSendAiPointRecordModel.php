<?php

declare (strict_types = 1);

namespace app\user\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段 
 * @property string $guid 唯一关键字段 
 * @property int $platformUserSysId 赠送用户id 
 * @property int $acceptPlatformUserSysId 接受AI点数的用户id 
 * @property string $merchantGuid 商家guid 
 * @property int $sendAiPoint 赠送AI点数 
 * @property int $sendTime 赠送时间 
 * @property int $createTime 创建时间戳 
 * @property int $updateTime 修改时间戳 
 * @property int $deletedAt 删除时间 
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class UserBigVipSendAiPointRecordModel extends BaseModel
{
    /**
    * @var string
    */
    protected $name = 'user_big_vip_send_ai_point_record';
    protected static bool $isGuid = true;

    public function acceptUser()
    {
        return $this->hasOne(UsersModel::class, 'sys_id', 'accept_platform_user_sys_id')
            ->field('sys_id, nickname, head_imgurl, mobile');
    }
}
