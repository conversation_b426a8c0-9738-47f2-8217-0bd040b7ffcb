<?php
/**
 * @author: xuzhengyang
 * @Time: 2023/3/20   17:44
 */


namespace app\user\request\api;

use app\Request;

class UserRequest extends Request
{
    protected array $msgs = [];

    protected function getRule(): array
    {
        return [
            'xcxSilenceLogin' => [
                'jsCode' => ['require'],
                'merchantGuid' => ['require']
            ],
            'smsCode' => [
                // "imageCode" => ['require'],
                "phone" => ['require'],
            ],
            'smsLogin' => [
                "phone" => ['require'],
                "smsCode" => ['require']
            ],
            'phoneAuthLogin' => [
                'jsCode' => ['require'],
                'getPhoneCode' => ['require'],
                'merchantGuid' => ['require'],
            ],
            'xiaoyiLoginCode' => [
                'merchantGuid' => ['require'],
            ],
        ];
    }

}