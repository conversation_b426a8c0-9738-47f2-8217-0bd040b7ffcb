<?php

declare(strict_types=1);

namespace app\zhanhui\controller\admin;

use app\admin\AdminBaseController;
use app\zhanhui\logic\admin\ParsePackageLogic;
use app\zhanhui\request\admin\ParsePackageRequest;

class ParsePackage extends AdminBaseController
{
    /**
     * 套餐列表
     * @param ParsePackageRequest $request
     * @param ParsePackageLogic $logic
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function index(ParsePackageRequest $request, ParsePackageLogic $logic): array
    {
        return $logic->index($request->param());
    }

    /**
     * 套餐详情
     * @param ParsePackageRequest $request
     * @param ParsePackageLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function detail(ParsePackageRequest $request, ParsePackageLogic $logic): array
    {
        return $logic->detail($request->param());
    }

    /**
     * 创建套餐
     * @param ParsePackageRequest $request
     * @param ParsePackageLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function create(ParsePackageRequest $request, ParsePackageLogic $logic): array
    {
        return $logic->create($request->param());
    }

    /**
     * 更新套餐
     * @param ParsePackageRequest $request
     * @param ParsePackageLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function update(ParsePackageRequest $request, ParsePackageLogic $logic): array
    {
        return $logic->update($request->param());
    }

    /**
     * 删除套餐
     * @param ParsePackageRequest $request
     * @param ParsePackageLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function delete(ParsePackageRequest $request, ParsePackageLogic $logic): array
    {
        return $logic->delete($request->param());
    }

    /**
     * 切换套餐状态
     * @param ParsePackageRequest $request
     * @param ParsePackageLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function status(ParsePackageRequest $request, ParsePackageLogic $logic): array
    {
        return $logic->status($request->param());
    }
}
