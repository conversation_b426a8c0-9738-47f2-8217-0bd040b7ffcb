<?php

declare(strict_types=1);

namespace app\zhanhui\controller\admin;

use app\admin\AdminBaseController;
use app\zhanhui\logic\admin\SupplementLogic;
use app\zhanhui\request\admin\SupplementRequest;

class Supplement extends AdminBaseController
{
    /**
     * 上传文档补充
     * @param SupplementRequest $request
     * @param SupplementLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function upload(SupplementRequest $request, SupplementLogic $logic): array
    {
        return $logic->upload($request->param());
    }

    /**
     * 获取补充历史记录
     * @param SupplementRequest $request
     * @param SupplementLogic $logic
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function history(SupplementRequest $request, SupplementLogic $logic): array
    {
        return $logic->history($request->param());
    }

    /**
     * 获取补充记录详情
     * @param SupplementRequest $request
     * @param SupplementLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function detail(SupplementRequest $request, SupplementLogic $logic): array
    {
        return $logic->detail($request->param());
    }

    /**
     * 获取解析次数套餐列表（仅供查看）
     * @param SupplementLogic $logic
     * @return array
     */
    public function parsePackages(SupplementLogic $logic): array
    {
        return $logic->getParsePackages();
    }
}
