<?php

declare(strict_types=1);

namespace app\zhanhui\controller\api;

use app\user\ApiBaseController;
use app\zhanhui\logic\api\SupplementLogic;
use app\zhanhui\request\api\SupplementRequest;

class Supplement extends ApiBaseController
{
    protected $middleware = [
        'token' => [],
        'must_login' => [],
        'signature' => [],
    ];

    /**
     * 上传文档补充
     * @param SupplementRequest $request
     * @param SupplementLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function upload(SupplementRequest $request, SupplementLogic $logic): array
    {
        return $logic->upload($request->param());
    }

    /**
     * 获取补充历史记录
     * @param SupplementRequest $request
     * @param SupplementLogic $logic
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function history(SupplementRequest $request, SupplementLogic $logic): array
    {
        return $logic->history($request->param());
    }

    /**
     * 获取补充记录详情
     * @param SupplementRequest $request
     * @param SupplementLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function detail(SupplementRequest $request, SupplementLogic $logic): array
    {
        return $logic->detail($request->param());
    }

    /**
     * 获取解析次数套餐列表
     * @param SupplementLogic $logic
     * @return array
     */
    public function parsePackages(SupplementLogic $logic): array
    {
        return $logic->getParsePackages();
    }

    /**
     * 购买解析次数套餐
     * @param SupplementRequest $request
     * @param SupplementLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function buyParsePackage(SupplementRequest $request, SupplementLogic $logic): array
    {
        return $logic->buyParsePackage($request->param());
    }

    /**
     * 获取展会剩余解析次数
     * @param SupplementRequest $request
     * @param SupplementLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function getParseCount(SupplementRequest $request, SupplementLogic $logic): array
    {
        return $logic->getParseCount($request->param());
    }

    /**
     * 支付成功回调
     * @param SupplementLogic $logic
     * @return array
     */
    public function payNotify(SupplementLogic $logic): array
    {
        $orderNo = input('post.out_trade_no', '');
        if (empty($orderNo)) {
            return ['code' => 'FAIL', 'message' => '订单号不能为空'];
        }

        $result = $logic->paySuccess($orderNo);
        
        return $result ? ['code' => 'SUCCESS'] : ['code' => 'FAIL'];
    }
}
