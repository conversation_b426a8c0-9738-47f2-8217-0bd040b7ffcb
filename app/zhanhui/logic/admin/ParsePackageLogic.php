<?php

declare(strict_types=1);

namespace app\zhanhui\logic\admin;

use app\constdir\SysErrorCode;
use app\zhanhui\models\MerchantZhanhuiParsePackageModel;

class ParsePackageLogic
{
    /**
     * 套餐列表
     * @param array $data
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function index(array $data): array
    {
        $page = $data['page'] ?? 1;
        $limit = $data['limit'] ?? 10;

        $query = MerchantZhanhuiParsePackageModel::getInstance()
            ->order('sort', 'asc')
            ->order('create_time', 'desc');

        $result = $query->paginate($limit, false, ['page' => $page]);
        $list = $result->toArray();

        // 格式化数据
        foreach ($list['data'] as &$item) {
            $item['statusText'] = MerchantZhanhuiParsePackageModel::getStatusText($item['status']);
            $item['createTimeText'] = date('Y-m-d H:i:s', $item['create_time']);
        }

        return $list;
    }

    /**
     * 套餐详情
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function detail(array $data): array
    {
        $package = MerchantZhanhuiParsePackageModel::getInstance()
            ->where('guid', $data['guid'])
            ->findOrEmpty();

        if ($package->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐不存在');
        }

        $packageData = $package->toArray();
        $packageData['statusText'] = MerchantZhanhuiParsePackageModel::getStatusText($packageData['status']);
        $packageData['createTimeText'] = date('Y-m-d H:i:s', $packageData['create_time']);

        return $packageData;
    }

    /**
     * 创建套餐
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function create(array $data): array
    {
        // 检查套餐名称是否重复
        $existPackage = MerchantZhanhuiParsePackageModel::getInstance()
            ->where('package_name', $data['packageName'])
            ->findOrEmpty();
        if (!$existPackage->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐名称已存在');
        }

        $package = new MerchantZhanhuiParsePackageModel();
        $package->package_name = $data['packageName'];
        $package->price = $data['price'];
        $package->parse_count = $data['parseCount'];
        $package->status = $data['status'] ?? MerchantZhanhuiParsePackageModel::STATUS_ENABLE;
        $package->sort = $data['sort'] ?? 0;
        $package->create_time = time();
        $package->save();

        return ['guid' => $package->guid];
    }

    /**
     * 更新套餐
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function update(array $data): array
    {
        $package = MerchantZhanhuiParsePackageModel::getInstance()
            ->where('guid', $data['guid'])
            ->findOrEmpty();

        if ($package->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐不存在');
        }

        // 检查套餐名称是否重复（排除自己）
        $existPackage = MerchantZhanhuiParsePackageModel::getInstance()
            ->where('package_name', $data['packageName'])
            ->where('guid', '<>', $data['guid'])
            ->findOrEmpty();

        if (!$existPackage->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐名称已存在');
        }

        $package->package_name = $data['packageName'];
        $package->price = $data['price'];
        $package->parse_count = $data['parseCount'];
        $package->status = $data['status'];
        $package->sort = $data['sort'];
        $package->update_time = time();
        $package->save();

        return [];
    }

    /**
     * 删除套餐
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function delete(array $data): array
    {
        $package = MerchantZhanhuiParsePackageModel::getInstance()
            ->where('guid', $data['guid'])
            ->findOrEmpty();

        if ($package->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐不存在');
        }

        $package->delete();

        return [];
    }

    /**
     * 切换套餐状态
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function status(array $data): array
    {
        $package = MerchantZhanhuiParsePackageModel::getInstance()
            ->where('guid', $data['guid'])
            ->findOrEmpty();

        if ($package->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐不存在');
        }

        $package->status = $data['status'];
        $package->update_time = time();
        $package->save();

        return [];
    }
}
