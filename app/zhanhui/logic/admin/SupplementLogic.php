<?php

declare(strict_types=1);

namespace app\zhanhui\logic\admin;

use app\constdir\SysErrorCode;
use app\zhanhui\models\MerchantZhanhuiModel;
use app\zhanhui\models\MerchantZhanhuiSupplementRecordModel;
use app\zhanhui\models\MerchantZhanhuiParsePackageModel;

class SupplementLogic
{
    /**
     * 上传文档补充
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function upload(array $data): array
    {
        // 验证展会是否存在
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $data['zhanhuiGuid'])
            ->findOrEmpty();
        
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }

        // 检查展会剩余解析次数
        if ($zhanhuiInfo->parse_count <= 0) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '解析次数不足，请先购买解析次数');
        }

        // 处理文件上传
        $file = $data['file'];
        // 简化文件上传逻辑，直接保存文件路径
        $uploadPath = 'uploads/zhanhui/supplement/' . date('Y/m/d/');
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }
        $fileName = time() . '_' . $file->getOriginalName();
        $filePath = $uploadPath . $fileName;
        $file->move($uploadPath, $fileName);

        // 创建补充记录
        $record = new MerchantZhanhuiSupplementRecordModel();
        $record->merchant_guid = $zhanhuiInfo->merchant_guid;
        $record->zhanhui_guid = $data['zhanhuiGuid'];
        $record->file_name = $file->getOriginalName();
        $record->file_path = $filePath;
        $record->supplement_type = $data['supplementType'];
        $record->target_modules = $data['targetModules'] ?? [];
        $record->is_sync_knowledge = $data['isSyncKnowledge'] ?? MerchantZhanhuiSupplementRecordModel::SYNC_KNOWLEDGE_YES;
        $record->run_status = MerchantZhanhuiSupplementRecordModel::STATUS_RUN_WAIT;
        $record->create_time = time();
        $record->save();

        return [
            'recordGuid' => $record->guid,
            'message' => '文档上传成功，正在处理中...'
        ];
    }

    /**
     * 获取补充历史记录
     * @param array $data
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function history(array $data): array
    {
        $page = $data['page'] ?? 1;
        $limit = $data['limit'] ?? 10;

        $query = MerchantZhanhuiSupplementRecordModel::getInstance()
            ->where('zhanhui_guid', $data['zhanhuiGuid'])
            ->order('create_time', 'desc');

        $result = $query->paginate($limit, false, ['page' => $page]);
        $list = $result->toArray();

        // 格式化数据
        foreach ($list['data'] as &$item) {
            $item['statusText'] = MerchantZhanhuiSupplementRecordModel::getStatusText($item['run_status']);
            $item['supplementTypeText'] = MerchantZhanhuiSupplementRecordModel::getSupplementTypeText($item['supplement_type']);
            $item['syncKnowledgeText'] = MerchantZhanhuiSupplementRecordModel::getSyncKnowledgeText($item['is_sync_knowledge']);
            $item['createTimeText'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['completeTimeText'] = $item['complete_time'] ? date('Y-m-d H:i:s', $item['complete_time']) : '';
        }

        return $list;
    }

    /**
     * 获取补充记录详情
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function detail(array $data): array
    {
        $record = MerchantZhanhuiSupplementRecordModel::getInstance()
            ->where('guid', $data['guid'])
            ->findOrEmpty();

        if ($record->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '记录不存在');
        }

        $recordData = $record->toArray();
        $recordData['statusText'] = MerchantZhanhuiSupplementRecordModel::getStatusText($recordData['run_status']);
        $recordData['supplementTypeText'] = MerchantZhanhuiSupplementRecordModel::getSupplementTypeText($recordData['supplement_type']);
        $recordData['syncKnowledgeText'] = MerchantZhanhuiSupplementRecordModel::getSyncKnowledgeText($recordData['is_sync_knowledge']);
        $recordData['createTimeText'] = date('Y-m-d H:i:s', $recordData['create_time']);
        $recordData['completeTimeText'] = $recordData['complete_time'] ? date('Y-m-d H:i:s', $recordData['complete_time']) : '';

        return $recordData;
    }

    /**
     * 获取解析次数套餐列表（仅供查看）
     * @return array
     */
    public function getParsePackages(): array
    {
        return MerchantZhanhuiParsePackageModel::getEnabledPackages();
    }
}
