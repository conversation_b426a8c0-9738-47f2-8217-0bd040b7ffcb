<?php

/**
 * @author: xuzhengyang
 * @Time: 2024/12/17   22:28
 */

namespace app\zhanhui\logic\api;

use app\constdir\SysErrorCode;
use app\libraries\exception\ApiException;
use app\libraries\service\dify\XinghuoDatabaseService;
use app\libraries\service\token\TokenService;
use app\libraries\service\wxpay\WxpayService;
use app\user\models\UsersModel;
use app\zhanhui\cache\ZhanhuiCache;
use app\zhanhui\models\MerchantZhanhuiAdminUserModel;
use app\zhanhui\models\MerchantZhanhuiBuyRecordInfoModel;
use app\zhanhui\models\MerchantZhanhuiBuyRecordModel;
use app\zhanhui\models\MerchantZhanhuiExchangeCodeModels;
use app\zhanhui\models\MerchantZhanhuiModel;
use app\zhanhui\models\ZhanhuiPackageModel;
use Cls\Log;

class OrderLogic
{
    /**
     * 创建订单
     * @param $data
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createOrder($data)
    {
        // 固定购买金额99元
        $data['payAmount'] = 99;

        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }
        // 获取套餐信息
        $package = ZhanhuiPackageModel::getInstance()
            ->where('guid', $data['packageGuid'])
            ->where('status', ZhanhuiPackageModel::STATUS_ONLINE)
            ->findOrEmpty();
        if ($package->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐不存在或已下架');
        }
        $merchantGuid = $userInfo['merchantGuid'];
        // 创建订单
        $buyOrder = new MerchantZhanhuiBuyRecordModel();
        $buyOrder->platformUserSysId = $userId;
        $buyOrder->zhanhuiGuid = '';
        $buyOrder->zhanhuiName = $data['zhanhuiName'];
        $buyOrder->merchantGuid = $merchantGuid;
        $buyOrder->orderNo = order_no('zhb');
        $buyOrder->orderStatus = MerchantZhanhuiBuyRecordModel::ORDER_STATUS_WAIT;
        $buyOrder->payStatus = MerchantZhanhuiBuyRecordModel::PAY_STATUS_UNPAID;
        $buyOrder->payAmount = $package->packagePrice;
        $buyOrder->packageGuid = $package->guid;

        // 存储套餐信息到订单
        $packageInfo = [
            'packageName' => $package->packageName,
            'packageDescription' => $package->packageDescription,
            'packagePrice' => $package->packagePrice,
            'validityPeriod' => $package->validityPeriod,
            'initialAiPoints' => $package->initialAiPoints,
            'documentSupplements' => $package->documentSupplements,
        ];
        $buyOrder->packageInfo = json_encode($packageInfo, JSON_UNESCAPED_UNICODE);
        $buyOrder->save();

        // 生成微信支付参数
        $openId = $userInfo['openId'];
        $payEnv = 'xcx';
        $payAmount = $data['payAmount'];
        $orderNo = $buyOrder->orderNo;
        $payService = new WxpayService($merchantGuid, $payEnv, $openId, 'sxt');
        $notifyUrl = config('app.app_url') . '/index/callback/zhanhuiBuyNotify';
        $payInfo = $payService->doWxPay('开通新展位', $payAmount, $orderNo, $notifyUrl);
        return [
            'orderNo' => $orderNo,
            'payInfo' => $payInfo
        ];
    }

    /**
     * 通过兑换码创建订单
     * @param $data
     * @return array
     * @throws ApiException
     */
    public function createOrderByCode($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }
        $merchantGuid = $userInfo['merchantGuid'];
        // 判断兑换码是否有效
        $existCode = MerchantZhanhuiExchangeCodeModels::getInstance()
            ->where('merchant_guid', $merchantGuid)
            ->where('exchange_code', $data['exchangeCode'])
            ->findOrEmpty();
        if ($existCode->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '兑换码无效');
        }
        if ($existCode->exchangeStatus == MerchantZhanhuiExchangeCodeModels::EXCHANGE_STATUS_USED) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '兑换码已使用');
        }
        // 创建订单
        $buyOrder = new MerchantZhanhuiBuyRecordModel();
        $buyOrder->platformUserSysId = $userId;
        $buyOrder->zhanhuiGuid = '';
        $buyOrder->zhanhuiName = $data['zhanhuiName'];
        $buyOrder->merchantGuid = $merchantGuid;
        $buyOrder->orderNo = order_no('zhb');
        $buyOrder->orderStatus = MerchantZhanhuiBuyRecordModel::ORDER_STATUS_WAIT;
        $buyOrder->payStatus = MerchantZhanhuiBuyRecordModel::PAY_STATUS_UNPAID;
        $buyOrder->payAmount = 0;
        $buyOrder->save();

        // 完成展会创建
        $createData = [
            'orderNo' => $buyOrder->orderNo,
        ];
        $zhanhuiInfo = $this->paySuccessCreateZhanhui($createData, $buyOrder);

        // 更新兑换码状态
        $existCode->exchangeStatus = MerchantZhanhuiExchangeCodeModels::EXCHANGE_STATUS_USED;
        $existCode->useZhanhuiGuid = $zhanhuiInfo['guid'];
        $existCode->save();
        return [
            'isPay' => true,
            'zhanhuiGuid' => $zhanhuiInfo['guid']
        ];
    }

    public function queryOrder($data)
    {
        $orderInfo = MerchantZhanhuiBuyRecordModel::getInstance()
            ->where('order_no', $data['orderNo'])
            ->findOrEmpty();
        if ($orderInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '订单不存在');
        }
        if ($orderInfo['payStatus'] == MerchantZhanhuiBuyRecordModel::PAY_STATUS_PAID) {
            return [
                 'isPay' => true,
                 'zhanhuiGuid' => $orderInfo['zhanhuiGuid']
            ];
        }
        $merchantGuid = $orderInfo['merchantGuid'];
        $payService = new WxpayService($merchantGuid);
        $payInfo = $payService->queryPayInfo($data['orderNo']);
        if ($payInfo['isPay']) {
            $zhanhuiInfo =  $this->paySuccessCreateZhanhui($data, $orderInfo);
            return [
                'isPay' => true,
                'zhanhuiGuid' => $zhanhuiInfo['guid']
            ];
        }
        return [
            'isPay' => false,
            'zhanhuiGuid' => ''
        ];
    }

    /**
     * 支付成功创建展会
     * @param $data
     * @param $orderInfo
     * @return MerchantZhanhuiModel
     */
    private function paySuccessCreateZhanhui($data, $orderInfo)
    {
        // 锁住当前订单，避免重复创建展会
        $lockKey = 'zhanhui_pay_success' . $data['orderNo'];
        ZhanhuiCache::getInstance()->lock($lockKey, 3);
        // 创建展会基本信息，后续由定时任务AI生成展会信息
        $zhanhuiInfo = new MerchantZhanhuiModel();
        $zhanhuiInfo->merchantGuid = $orderInfo['merchantGuid'];
        $zhanhuiInfo->name = $orderInfo['zhanhuiName'];
        $zhanhuiInfo->shortName = $orderInfo['zhanhuiName'];
        $zhanhuiInfo->description = '等待AI生成中...';
        $zhanhuiInfo->createStatus = MerchantZhanhuiModel::CREATE_STATUS_WAIT;
        $zhanhuiInfo->endIsShow = 1;
        $zhanhuiInfo->startTime = time();

        // 根据套餐信息设置展位属性
        if (!empty($orderInfo['packageInfo'])) {
            $packageInfo = $orderInfo['packageInfo'];
            $zhanhuiInfo->endTime = time() + 86400 * $packageInfo['validityPeriod'];
            $zhanhuiInfo->aiPoint = $packageInfo['initialAiPoints'];
            $zhanhuiInfo->documentSupplements = $packageInfo['documentSupplements'];
        } else {
            // 兼容旧版本固定配置
            $zhanhuiInfo->endTime = time() + 86400 * 180;
            $zhanhuiInfo->aiPoint = 10000;
            $zhanhuiInfo->documentSupplements = 0;
        }
        $zhanhuiInfo->status = MerchantZhanhuiModel::STATUS_DISABLE;
        $zhanhuiInfo->save();

        $zhanhuiGuid = $zhanhuiInfo->guid;
        $orderInfo->payStatus = MerchantZhanhuiBuyRecordModel::PAY_STATUS_PAID;
        $orderInfo->zhanhuiGuid = $zhanhuiGuid;
        $orderInfo->save();

        // 添加该用户到该展会的管理员中
        $zhanhuiAdminModel = new MerchantZhanhuiAdminUserModel();
        $zhanhuiAdminModel->merchantGuid = $orderInfo['merchantGuid'];
        $zhanhuiAdminModel->zhanhuiGuid = $zhanhuiGuid;
        $zhanhuiAdminModel->platformUserSysId = $orderInfo['platformUserSysId'];
        $zhanhuiAdminModel->save();

        return $zhanhuiInfo;
    }

    /**
     * 查询展会订单数据准备情况
     * @throws ApiException
     */
    public function orderInfo($params)
    {
        $orderInfo = MerchantZhanhuiBuyRecordModel::getInstance()
            ->where('zhanhui_guid', $params['zhanhuiGuid'])
            ->findOrEmpty();
        if ($orderInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '订单不存在');
        }
        // 查询订单是否已支付
        if ($orderInfo['payStatus'] != MerchantZhanhuiBuyRecordModel::PAY_STATUS_PAID) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '订单未支付，未生成展会信息');
        }
        // 获取当前订单的处理进度
        $recordInfo = MerchantZhanhuiBuyRecordInfoModel::getInstance()
            ->where('buy_record_order_no', $orderInfo['orderNo'])
            ->findOrEmpty();
        if ($recordInfo->isEmpty()) {
           // 创建订单对应的展会信息
            $recordInfo = new MerchantZhanhuiBuyRecordInfoModel();
            $recordInfo->merchantGuid = $orderInfo['merchantGuid'];
            $recordInfo->buyRecordOrderNo = $orderInfo['orderNo'];
            $recordInfo->fillProgress = MerchantZhanhuiBuyRecordInfoModel::FILL_PROGRESS_FILE;
            $recordInfo->modules = [];
            $recordInfo->save();

            return [
                'fillProgress' => 2,
                'zhanhuiGuid' => $orderInfo['zhanhuiGuid'],
                'order_no' => $orderInfo['orderNo'],
                'name' => $orderInfo['zhanhuiName'],
                'modules_1_name' => '',
                'modules_1_desc' => '',
                'modules_2_name' => '',
                'modules_2_desc' => '',
                'modules_3_name' => '',
                'modules_3_desc' => '',
                'knowledge_file_name' => '',
                'knowledge_file_path' => '',
            ];
        } else {
            return [
                'fillProgress' => $recordInfo['fillProgress'],
                'zhanhuiGuid' => $orderInfo['zhanhuiGuid'],
                'order_no' => $orderInfo['orderNo'],
                'name' => $orderInfo['zhanhuiName'],
                'themeDesc' => $recordInfo['themeDesc'],
                'modules_1_name' => $recordInfo['modules'][0]['name'] ?? '',
                'modules_1_desc' => $recordInfo['modules'][0]['desc'] ?? '',
                'modules_2_name' => $recordInfo['modules'][1]['name'] ?? '',
                'modules_2_desc' => $recordInfo['modules'][1]['desc'] ?? '',
                'modules_3_name' => $recordInfo['modules'][2]['name'] ?? '',
                'modules_3_desc' => $recordInfo['modules'][2]['desc'] ?? '',
                'knowledgeFileName' => $recordInfo['knowledgeFileName'],
                'knowledgeFilePath' => $recordInfo['knowledgeFilePath'],
            ];
        }
    }

    /**
     * 更新展会订单数据准备情况
     * @param $params
     * @return array
     * @throws ApiException
     */
    public function orderInfoUpdate($params)
    {
        LogInfo('orderInfoUpdate', 'params', '更新展会资料数据更新', [
            'params' => $params
        ]);
        $orderInfo = MerchantZhanhuiBuyRecordModel::getInstance()
            ->where('zhanhui_guid', $params['zhanhuiGuid'])
            ->findOrEmpty();
        if ($orderInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '订单不存在');
        }
        // 获取当前订单的处理进度
        $recordInfo = MerchantZhanhuiBuyRecordInfoModel::getInstance()
            ->where('buy_record_order_no', $orderInfo['orderNo'])
            ->findOrEmpty();
        if ($recordInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会信息初始化失败，请联系管理员');
        }
        // 完成步骤
        // if ($recordInfo['fillProgress'] == MerchantZhanhuiBuyRecordInfoModel::FILL_PROGRESS_FILE) {
        //     if (empty($params['knowledgeFilePath'])) {
        //         throwException(SysErrorCode::SYS_PARAMS_ERROR, '请上传知识库文件');
        //     }
        // }
        // 处理modules数据
        $modules = [];
        for ($i = 1; $i <= 3; $i++) {
            $modules[] = [
                'name' => $params['modules_' . $i . '_name'] ?? '',
                'desc' => $params['modules_' . $i . '_desc'] ?? ''
            ];
        }
        $recordInfo->themeDesc = $params['themeDesc'] ?? '';
        $recordInfo->fillProgress = $params['fillProgress'] ?? '';
        $recordInfo->modules = $modules;
        $recordInfo->knowledgeFileName = $params['knowledgeFileName'] ?? '';
        $recordInfo->knowledgeFilePath = $params['knowledgeFilePath'] ?? '';
        $recordInfo->save();

        if ($params['knowledgeFilePath']) {
            $result =  XinghuoDatabaseService::getInstance()->uploadFile($params['knowledgeFilePath']);
            if (!$result || empty($result['fileId'])) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '知识库文件初始化失败，请联系管理员');
            } else {
                $recordInfo->xinghuoFileId = $result['fileId'];
                $recordInfo->save();
            }

            // 更新订单状态为数据准备完成
            $orderInfo->orderStatus = MerchantZhanhuiBuyRecordModel::ORDER_STATUS_READY;
            $orderInfo->save();
        }

        return [];
    }

    /**
     * 展会订单列表
     * @param $params
     * @return array
     */
    public function buyLists($params)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }
        $merchantGuid = $userInfo['merchantGuid'];
        try {
            // 1. 获取用户自己创建的展会订单
            $orderList = MerchantZhanhuiBuyRecordModel::getInstance()
                ->with([
                    'recordInfo',
                    'zhanhui'
                ])
                ->where('merchant_guid', $merchantGuid)
                ->where('pay_status', MerchantZhanhuiBuyRecordModel::PAY_STATUS_PAID)
                ->where('platform_user_sys_id', $userId)
                ->order('create_time', 'desc')
                ->select();

            $list = [];
            $existZhanhuiGuids = []; // 记录已存在的展会GUID，避免重复

            // 处理用户自己创建的展会
            foreach ($orderList as $order) {
                $list[] = [
                    'zhanhuiGuid' => $order['zhanhuiGuid'],
                    'zhanhuiName' => $order['zhanhuiName'],
                    'orderStatus' => $order['orderStatus'],
                    'cover' => $order['zhanhui']['cover'],
                    'createTime' => $order['create_time'],
                ];
                $existZhanhuiGuids[] = $order['zhanhuiGuid'];
            }

            // 2. 获取用户作为管理员的展会
            $adminZhanhuiList = MerchantZhanhuiAdminUserModel::getInstance()
                ->with(['zhanhui' => function($query) {
                    $query->field('guid,name,cover,create_time');
                }])
                ->where('merchant_guid', $merchantGuid)
                ->where('platform_user_sys_id', $userId)
                ->select();

            // 处理用户作为管理员的展会
            foreach ($adminZhanhuiList as $adminZhanhui) {
                $zhanhuiGuid = $adminZhanhui['zhanhuiGuid'];

                // 避免重复添加（如果用户既是创建者又是管理员）
                if (in_array($zhanhuiGuid, $existZhanhuiGuids)) {
                    continue;
                }

                // 查找对应的订单状态，如果没有订单则设为已完成
                $orderStatus = MerchantZhanhuiBuyRecordModel::ORDER_STATUS_SUCCESS;
                $createTime = $adminZhanhui['create_time'];

                // 尝试查找对应的订单
                $relatedOrder = MerchantZhanhuiBuyRecordModel::getInstance()
                    ->where('zhanhui_guid', $zhanhuiGuid)
                    ->where('pay_status', MerchantZhanhuiBuyRecordModel::PAY_STATUS_PAID)
                    ->findOrEmpty();

                if (!$relatedOrder->isEmpty()) {
                    $orderStatus = $relatedOrder['orderStatus'];
                    $createTime = $relatedOrder['create_time'];
                }

                $list[] = [
                    'zhanhuiGuid' => $zhanhuiGuid,
                    'zhanhuiName' => $adminZhanhui['zhanhui']['name'] ?? '',
                    'orderStatus' => $orderStatus,
                    'cover' => $adminZhanhui['zhanhui']['cover'] ?? '',
                    'createTime' => $createTime,
                ];
            }

            // 3. 按创建时间倒序排列
            usort($list, function($a, $b) {
                return $b['createTime'] - $a['createTime'];
            });

            return $list;
        }catch (\Exception $e) {
            LogInfo('zhanhui', '展会订单列表', '获取展会列表失败', [
                'e' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throwException(SysErrorCode::SYS_ERROR_CODE, '获取展会列表失败');
        }
    }

    /**
     * 根据套餐创建订单
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createOrderByPackage(array $data): array
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }

        // 获取套餐信息
        $package = ZhanhuiPackageModel::getInstance()
            ->where('guid', $data['packageGuid'])
            ->where('status', ZhanhuiPackageModel::STATUS_ONLINE)
            ->findOrEmpty();
        if ($package->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐不存在或已下架');
        }

        $merchantGuid = $userInfo['merchantGuid'];

        // 创建订单
        $buyOrder = new MerchantZhanhuiBuyRecordModel();
        $buyOrder->platformUserSysId = $userId;
        $buyOrder->zhanhuiGuid = '';
        $buyOrder->zhanhuiName = $data['zhanhuiName'];
        $buyOrder->merchantGuid = $merchantGuid;
        $buyOrder->orderNo = order_no('zhb');
        $buyOrder->orderStatus = MerchantZhanhuiBuyRecordModel::ORDER_STATUS_WAIT;
        $buyOrder->payStatus = MerchantZhanhuiBuyRecordModel::PAY_STATUS_UNPAID;
        $buyOrder->payAmount = $package->packagePrice;
        $buyOrder->packageGuid = $package->guid;

        // 存储套餐信息到订单
        $packageInfo = [
            'packageName' => $package->packageName,
            'packageDescription' => $package->packageDescription,
            'packagePrice' => $package->packagePrice,
            'validityPeriod' => $package->validityPeriod,
            'initialAiPoints' => $package->initialAiPoints,
            'documentSupplements' => $package->documentSupplements,
        ];
        $buyOrder->packageInfo = json_encode($packageInfo, JSON_UNESCAPED_UNICODE);
        $buyOrder->save();

        // 生成微信支付参数
        $openId = $userInfo['openId'];
        $payEnv = 'xcx';
        $payAmount = $buyOrder->payAmount;
        $orderNo = $buyOrder->orderNo;
        $payService = new WxpayService($merchantGuid, $payEnv, $openId, 'sxt');
        $notifyUrl = config('app.app_url') . '/index/callback/zhanhuiBuyNotify';
        $payInfo = $payService->doWxPay('开通新展位', $payAmount, $orderNo, $notifyUrl);
        return [
            'orderNo' => $orderNo,
            'payInfo' => $payInfo
        ];
    }
}
