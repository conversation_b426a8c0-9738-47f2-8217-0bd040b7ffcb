<?php

declare(strict_types=1);

namespace app\zhanhui\logic\api;

use app\constdir\SysErrorCode;
use app\libraries\service\file\FileService;
use app\libraries\service\token\TokenService;
use app\zhanhui\models\MerchantZhanhuiModel;
use app\zhanhui\models\MerchantZhanhuiSupplementRecordModel;
use app\zhanhui\models\MerchantZhanhuiParsePackageModel;
use app\zhanhui\models\MerchantZhanhuiParseOrderModel;
use app\zhanhui\models\MerchantZhanhuiConfigModel;
use app\zhanhui\models\MerchantZhanhuiAdminUserModel;
use app\libraries\service\alipay\AlipayService;
use app\libraries\service\wxpay\WxpayService;

class SupplementLogic
{
    /**
     * 获取用户所属的商户ID
     * @return string
     * @throws \app\libraries\exception\ApiException
     */
    private function getMerchantId(): string
    {
        $platformUserId = TokenService::getInstance()->getTokenEntity()->userId;
        // 通过用户ID获取商户GUID
        $adminUser = MerchantZhanhuiAdminUserModel::getInstance()
            ->where('platform_user_sys_id', $platformUserId)
            ->findOrEmpty();
        if ($adminUser->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户未绑定展会管理权限');
        }
        
        // 获取展会信息来获取商户GUID
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $adminUser->zhanhui_guid)
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会信息不存在');
        }
        
        return $zhanhuiInfo->merchant_guid;
    }

    /**
     * 验证用户是否有展会管理权限
     * @param string $zhanhuiGuid
     * @return bool
     * @throws \app\libraries\exception\ApiException
     */
    private function checkZhanhuiPermission(string $zhanhuiGuid): bool
    {
        $platformUserId = TokenService::getInstance()->getTokenEntity()->userId;
        $adminUser = MerchantZhanhuiAdminUserModel::getInstance()
            ->where('platform_user_sys_id', $platformUserId)
            ->where('zhanhui_guid', $zhanhuiGuid)
            ->findOrEmpty();
        if ($adminUser->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '您没有该展会的管理权限');
        }
        
        return true;
    }

    /**
     * 上传文档补充
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function upload(array $data): array
    {
        // 验证用户权限
        $this->checkZhanhuiPermission($data['zhanhuiGuid']);
        
        // 验证展会是否存在
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $data['zhanhuiGuid'])
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }
        // 检查展会剩余解析次数
        if ($zhanhuiInfo->parse_count <= 0) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '解析次数不足，请先购买解析次数');
        }

        // 创建补充记录
        $record = new MerchantZhanhuiSupplementRecordModel();
        $record->merchant_guid = $zhanhuiInfo->merchant_guid;
        $record->zhanhui_guid = $data['zhanhuiGuid'];
        $record->file_name = $data['supplementFileName'];
        $record->file_path = $data['supplementFilePath'];
        $record->supplement_type = $data['supplementType'];
        $record->target_modules = $data['targetModules'] ?? [];
        $record->is_sync_knowledge = $data['isSyncKnowledge'] ?? MerchantZhanhuiSupplementRecordModel::SYNC_KNOWLEDGE_NO;
        $record->run_status = MerchantZhanhuiSupplementRecordModel::STATUS_RUN_WAIT;
        $record->create_time = time();
        $record->save();

        return [
            'recordGuid' => $record->guid,
            'message' => '文档上传成功，正在处理中...'
        ];
    }

    /**
     * 获取补充历史记录
     * @param array $data
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function history(array $data): array
    {
        // 验证用户权限
        $this->checkZhanhuiPermission($data['zhanhuiGuid']);

        $list = MerchantZhanhuiSupplementRecordModel::getInstance()
            ->where('zhanhui_guid', $data['zhanhuiGuid'])
            ->order('create_time', 'desc')
            ->paginate($params['pageSize'] ?? 10)
            ->toArray();
        // 格式化数据
        foreach ($list['data'] as &$item) {
            $item['statusText'] = MerchantZhanhuiSupplementRecordModel::getStatusText($item['run_status']);
            $item['supplementTypeText'] = MerchantZhanhuiSupplementRecordModel::getSupplementTypeText($item['supplement_type']);
            $item['syncKnowledgeText'] = MerchantZhanhuiSupplementRecordModel::getSyncKnowledgeText($item['is_sync_knowledge']);
            $item['createTimeText'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['completeTimeText'] = $item['complete_time'] ? date('Y-m-d H:i:s', $item['complete_time']) : '';
        }
        return $list;
    }

    /**
     * 获取补充记录详情
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function detail(array $data): array
    {
        $record = MerchantZhanhuiSupplementRecordModel::getInstance()
            ->where('guid', $data['guid'])
            ->findOrEmpty();
        if ($record->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '记录不存在');
        }

        // 验证用户权限
        $this->checkZhanhuiPermission($record->zhanhui_guid);

        $recordData = $record->toArray();
        $recordData['statusText'] = MerchantZhanhuiSupplementRecordModel::getStatusText($recordData['run_status']);
        $recordData['supplementTypeText'] = MerchantZhanhuiSupplementRecordModel::getSupplementTypeText($recordData['supplement_type']);
        $recordData['syncKnowledgeText'] = MerchantZhanhuiSupplementRecordModel::getSyncKnowledgeText($recordData['is_sync_knowledge']);
        $recordData['createTimeText'] = date('Y-m-d H:i:s', $recordData['create_time']);
        $recordData['completeTimeText'] = $recordData['complete_time'] ? date('Y-m-d H:i:s', $recordData['complete_time']) : '';

        return $recordData;
    }

    /**
     * 获取解析次数套餐列表
     * @return array
     */
    public function getParsePackages(): array
    {
        return MerchantZhanhuiParsePackageModel::getEnabledPackages();
    }

    /**
     * 获取展会剩余解析次数
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function getParseCount(array $data): array
    {
        // 验证用户权限
        $this->checkZhanhuiPermission($data['zhanhuiGuid']);
        
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $data['zhanhuiGuid'])
            ->field('parse_count')
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }

        return [
            'parseCount' => $zhanhuiInfo->parse_count
        ];
    }

    /**
     * 购买解析次数套餐
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function buyParsePackage(array $data): array
    {
        // 验证用户权限
        $this->checkZhanhuiPermission($data['zhanhuiGuid']);
        
        // 验证展会是否存在
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $data['zhanhuiGuid'])
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }

        // 验证套餐是否存在
        $package = MerchantZhanhuiParsePackageModel::getInstance()
            ->where('guid', $data['packageGuid'])
            ->where('status', MerchantZhanhuiParsePackageModel::STATUS_ENABLE)
            ->findOrEmpty();
        if ($package->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐不存在或已下架');
        }

        // 创建订单
        $order = new MerchantZhanhuiParseOrderModel();
        $order->merchant_guid = $zhanhuiInfo->merchant_guid;
        $order->zhanhui_guid = $data['zhanhuiGuid'];
        $order->package_guid = $data['packageGuid'];
        $order->order_no = MerchantZhanhuiParseOrderModel::generateOrderNo();
        $order->package_name = $package->package_name;
        $order->price = $package->price;
        $order->parse_count = $package->parse_count;
        $order->pay_status = MerchantZhanhuiParseOrderModel::PAY_STATUS_WAIT;
        $order->pay_type = $data['payType'];
        $order->create_time = time();
        $order->save();

        // 发起支付
        $payResult = $this->initiatePay($order, $data['payType']);

        return [
            'orderNo' => $order->order_no,
            'payData' => $payResult
        ];
    }

    /**
     * 发起支付
     * @param MerchantZhanhuiParseOrderModel $order
     * @param int $payType
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    private function initiatePay(MerchantZhanhuiParseOrderModel $order, int $payType): array
    {
        $payDes = '购买解析次数套餐：' . $order->package_name;
        $notifyUrl = config('app.app_host') . '/api/zhanhui/supplement/pay-notify';

        try {
            if ($payType == MerchantZhanhuiParseOrderModel::PAY_TYPE_WECHAT) {
                // 微信支付
                $wxpayService = new WxpayService($order->merchant_guid);
                return $wxpayService->doWxPay($payDes, $order->price, $order->order_no, $notifyUrl);
            } else {
                // 支付宝支付
                $alipayService = new AlipayService();
                return $alipayService->doWapPay($order->merchant_guid, $payDes, $order->price, $order->order_no, $notifyUrl);
            }
        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '支付发起失败：' . $e->getMessage());
        }
    }

    /**
     * 支付成功回调处理
     * @param string $orderNo
     * @return bool
     */
    public function paySuccess(string $orderNo): bool
    {
        $order = MerchantZhanhuiParseOrderModel::getInstance()
            ->where('order_no', $orderNo)
            ->findOrEmpty();
        if ($order->isEmpty() || $order->pay_status == MerchantZhanhuiParseOrderModel::PAY_STATUS_SUCCESS) {
            return false;
        }

        // 更新订单状态
        $order->pay_status = MerchantZhanhuiParseOrderModel::PAY_STATUS_SUCCESS;
        $order->pay_time = time();
        $order->save();

        // 增加展会解析次数
        MerchantZhanhuiModel::getInstance()
            ->where('guid', $order->zhanhui_guid)
            ->inc('parse_count', $order->parse_count)
            ->update();

        return true;
    }
}
