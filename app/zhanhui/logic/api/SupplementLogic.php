<?php

declare(strict_types=1);

namespace app\zhanhui\logic\api;

use app\constdir\SysErrorCode;
use app\libraries\service\file\FileService;
use app\libraries\service\token\TokenService;
use app\zhanhui\models\MerchantZhanhuiModel;
use app\zhanhui\models\MerchantZhanhuiSupplementRecordModel;
use app\zhanhui\models\MerchantZhanhuiParsePackageModel;
use app\zhanhui\models\MerchantZhanhuiParseOrderModel;
use app\zhanhui\models\MerchantZhanhuiAdminUserModel;
use app\libraries\service\alipay\AlipayService;
use app\libraries\service\wxpay\WxpayService;

class SupplementLogic
{
    /**
     * 验证用户是否有展会管理权限
     * @param string $zhanhuiGuid
     * @return bool
     * @throws \app\libraries\exception\ApiException
     */
    private function checkZhanhuiPermission(string $zhanhuiGuid): bool
    {
        $platformUserId = TokenService::getInstance()->getTokenEntity()->userId;
        $adminUser = MerchantZhanhuiAdminUserModel::getInstance()
            ->where('platform_user_sys_id', $platformUserId)
            ->where('zhanhui_guid', $zhanhuiGuid)
            ->findOrEmpty();
        if ($adminUser->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '您没有该展会的管理权限');
        }
        
        return true;
    }

    /**
     * 执行AI主页模块知识补充
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function execute(array $data): array
    {
        // 验证用户权限
//        $this->checkZhanhuiPermission($data['zhanhuiGuid']);
        
        // 验证展会是否存在
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $data['zhanhuiGuid'])
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }
        // 检查展会剩余解析次数
        if ($zhanhuiInfo->parse_count <= 0) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '解析次数不足，请先购买解析次数');
        }

        // 如果是手动选择模块，需要验证模块内容是否存在，若为空则需要拦截
        if (
            $data['supplementType'] == MerchantZhanhuiSupplementRecordModel::SUPPLEMENT_TYPE_MANUAL
            && empty($data['targetModules'])
        ) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '请选择要补充的模块');
        }


        // 创建补充记录
        $record = new MerchantZhanhuiSupplementRecordModel();
        $record->merchant_guid = $zhanhuiInfo->merchant_guid;
        $record->zhanhui_guid = $data['zhanhuiGuid'];
        $record->file_name = $data['supplementFileName'];
        $record->file_path = $data['supplementFilePath'];
        $record->supplement_type = $data['supplementType'];
        $record->target_modules = $data['targetModules'] ?? [];
        $record->is_sync_knowledge = $data['isSyncKnowledge'] ?? MerchantZhanhuiSupplementRecordModel::SYNC_KNOWLEDGE_NO;
        $record->run_status = MerchantZhanhuiSupplementRecordModel::STATUS_RUN_WAIT;
        $record->create_time = time();
        $record->save();

        // 扣除解析次数
        $zhanhuiInfo->parse_count = $zhanhuiInfo->parse_count - 1;
        $zhanhuiInfo->save();

        return [
            'recordGuid' => $record->guid,
            'message' => '解析任务提交成功，正在处理中...'
        ];
    }

    /**
     * 获取补充历史记录
     * @param array $data
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function history(array $data): array
    {
        $list = MerchantZhanhuiSupplementRecordModel::getInstance()
            ->with('zhanhui')
            ->where('zhanhui_guid', $data['zhanhuiGuid'])
            ->order('create_time', 'desc')
            ->paginate($params['pageSize'] ?? 10)
            ->toArray();
        // 格式化数据
        foreach ($list['data'] as &$item) {
            $item['zhanhuiName'] = $item['zhanhui']['name'] ?? '';
            $item['statusText'] = MerchantZhanhuiSupplementRecordModel::getStatusText($item['runStatus']);
            $item['supplementTypeText'] = MerchantZhanhuiSupplementRecordModel::getSupplementTypeText($item['supplementType']);
            $item['syncKnowledgeText'] = MerchantZhanhuiSupplementRecordModel::getSyncKnowledgeText($item['isSyncKnowledge']);
            $item['createTimeText'] = date('Y-m-d H:i:s', $item['createTime']);
            $item['completeTimeText'] = $item['completeTime'] ? date('Y-m-d H:i:s', $item['completeTime']) : '';

            unset($item['zhanhui']);
        }
        return $list;
    }

    /**
     * 获取补充记录详情
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function detail(array $data): array
    {
        $record = MerchantZhanhuiSupplementRecordModel::getInstance()
            ->where('guid', $data['guid'])
            ->findOrEmpty();
        if ($record->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '记录不存在');
        }

        // 验证用户权限
        $this->checkZhanhuiPermission($record->zhanhui_guid);

        $recordData = $record->toArray();
        $recordData['statusText'] = MerchantZhanhuiSupplementRecordModel::getStatusText($recordData['run_status']);
        $recordData['supplementTypeText'] = MerchantZhanhuiSupplementRecordModel::getSupplementTypeText($recordData['supplement_type']);
        $recordData['syncKnowledgeText'] = MerchantZhanhuiSupplementRecordModel::getSyncKnowledgeText($recordData['is_sync_knowledge']);
        $recordData['createTimeText'] = date('Y-m-d H:i:s', $recordData['create_time']);
        $recordData['completeTimeText'] = $recordData['complete_time'] ? date('Y-m-d H:i:s', $recordData['complete_time']) : '';

        return $recordData;
    }

    /**
     * 获取解析次数套餐列表
     * @return array
     */
    public function getParsePackages(): array
    {
        $lists = MerchantZhanhuiParsePackageModel::getEnabledPackages();

        // 将单位分转换为元
        foreach ($lists as &$item) {
            $item['price'] = fen_to_yuan($item['price']);
        }

        return $lists;
    }

    /**
     * 获取展会剩余解析次数
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function getParseCount(array $data): array
    {
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $data['zhanhuiGuid'])
            ->field('parse_count')
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            return [
                'parseCount' => 0
            ];
        }

        return [
            'parseCount' => $zhanhuiInfo->parse_count ?? 0
        ];
    }

    /**
     * 购买解析次数套餐
     * @param array $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function buyParsePackage(array $data): array
    {
        // 默认使用微信支付
        $data['payType'] =  MerchantZhanhuiParseOrderModel::PAY_TYPE_WECHAT;
        // 验证用户权限
        $this->checkZhanhuiPermission($data['zhanhuiGuid']);
        
        // 验证展会是否存在
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $data['zhanhuiGuid'])
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }

        // 验证套餐是否存在
        $package = MerchantZhanhuiParsePackageModel::getInstance()
            ->where('guid', $data['packageGuid'])
            ->where('status', MerchantZhanhuiParsePackageModel::STATUS_ENABLE)
            ->findOrEmpty();
        if ($package->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐不存在或已下架');
        }

        // 创建订单
        $order = new MerchantZhanhuiParseOrderModel();
        $order->merchant_guid = $zhanhuiInfo->merchant_guid;
        $order->zhanhui_guid = $data['zhanhuiGuid'];
        $order->package_guid = $data['packageGuid'];
        $order->order_no = MerchantZhanhuiParseOrderModel::generateOrderNo();
        $order->package_name = $package->package_name;
        $order->price = $package->price;
        $order->parse_count = $package->parse_count;
        $order->pay_status = MerchantZhanhuiParseOrderModel::PAY_STATUS_WAIT;
        $order->pay_type = $data['payType'];
        $order->create_time = time();
        $order->save();

        // 发起支付
        $payResult = $this->initiatePay($order, $data['payType']);

        return [
            'orderNo' => $order->order_no,
            'payData' => $payResult
        ];
    }

    /**
     * 发起支付
     * @param MerchantZhanhuiParseOrderModel $order
     * @param int $payType
     * @return array|string|void|\提交表单HTML文本|\构建好的、签名后的最终跳转URL（GET）或String形式的form（POST）
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \app\libraries\exception\ApiException
     */
    private function initiatePay(MerchantZhanhuiParseOrderModel $order, int $payType = MerchantZhanhuiParseOrderModel::PAY_TYPE_WECHAT)
    {
        $payDes = '购买解析次数套餐：' . $order->package_name;

        try {
            if ($payType == MerchantZhanhuiParseOrderModel::PAY_TYPE_ALIPAY) {
                // 支付宝支付
                $notifyUrl = config('app.app_url') . '/index/callback/zhanhuiParsePackageAlipayNotify';
                $alipayService = new AlipayService();
                return $alipayService->doWapPay($order->merchant_guid, $payDes, $order->price, $order->order_no, $notifyUrl);
            } else {
                // 微信支付
                $notifyUrl = config('app.app_url') . '/index/callback/zhanhuiParsePackageNotify';
                $wxpayService = new WxpayService($order->merchant_guid);
                return $wxpayService->doWxPay($payDes, $order->price, $order->order_no, $notifyUrl);
            }
        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '支付发起失败：' . $e->getMessage());
        }
    }

    /**
     * 支付成功回调处理
     * @param string $orderNo
     * @return bool
     */
    public function paySuccess(string $orderNo): bool
    {
        LogInfo('SupplementLogic', '解析套餐支付回调', '开始处理', ['orderNo' => $orderNo]);

        $order = MerchantZhanhuiParseOrderModel::getInstance()
            ->where('order_no', $orderNo)
            ->findOrEmpty();

        if ($order->isEmpty()) {
            LogError('SupplementLogic', '解析套餐支付回调', '订单不存在', ['orderNo' => $orderNo]);
            return false;
        }

        if ($order->pay_status == MerchantZhanhuiParseOrderModel::PAY_STATUS_SUCCESS) {
            LogInfo('SupplementLogic', '解析套餐支付回调', '订单已支付', ['orderNo' => $orderNo]);
            return true; // 已支付成功，返回true
        }

        try {
            // 使用数据库事务确保数据一致性
            \think\facade\Db::startTrans();

            // 更新订单状态
            $order->pay_status = MerchantZhanhuiParseOrderModel::PAY_STATUS_SUCCESS;
            $order->pay_time = time();
            $order->save();

            // 增加展会解析次数
            $updateResult = MerchantZhanhuiModel::getInstance()
                ->where('guid', $order->zhanhui_guid)
                ->inc('parse_count', $order->parse_count)
                ->update();

            if ($updateResult === false) {
                throw new \Exception('更新展会解析次数失败');
            }

            \think\facade\Db::commit();

            LogInfo('SupplementLogic', '解析套餐支付回调', '处理成功', [
                'orderNo' => $orderNo,
                'zhanhuiGuid' => $order->zhanhui_guid,
                'addParseCount' => $order->parse_count
            ]);

            return true;

        } catch (\Exception $e) {
            \think\facade\Db::rollback();

            LogError('SupplementLogic', '解析套餐支付回调', '处理失败', [
                'orderNo' => $orderNo,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return false;
        }
    }
}
