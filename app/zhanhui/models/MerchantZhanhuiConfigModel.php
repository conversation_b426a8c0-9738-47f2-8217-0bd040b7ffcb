<?php

declare(strict_types=1);

namespace app\zhanhui\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商家guid
 * @property string $zhanhuiGuid 展会guid
 * @property string $topicBannerTitle 议题banner标题
 * @property string $guestBannerTitle 嘉宾banner标题
 * @property string $recommendCompanyTitle 推荐企业banner标题
 * @property string $welcomeText 对话欢迎文案
 * @property string $aboutZhanhuiTitle 关于展会标题
 * @property int $isRequirePhone 是否要求登记手机号码
 * @property string $merchantKnowledgeGuid 商家知识库guid
 * @property string $knowledgePrompt 知识库使用提示词
 * @property string $aiChatPrompt AI对话提示词
 * @property string $aiChatModel AI对话模型
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class MerchantZhanhuiConfigModel extends BaseModel
{
    /**
    * @var string
    */
    protected $name = 'merchant_zhanhui_config';
    protected static bool $isGuid = true;
}
