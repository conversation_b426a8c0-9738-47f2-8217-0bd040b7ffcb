<?php

declare(strict_types=1);

namespace app\zhanhui\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商家guid
 * @property string $zhanhuiGuid 展会guid
 * @property string $question 问题
 * @property string $aiChatQuestion 聊天对话页展示的问题
 * @property int $answerType 答案类型：1-富文本，2-图片，3-链接，4-视频
 * @property string $answer 答案
 * @property int $position 展示位置：1-首页滚动，2-轮播图位置，3-欢迎语下方常见问题，4-底部常用工具
 * @property string $iconType 问题图标类型: notice-通知，msg-消息，hot-热点，recommend-推荐，tool-工具
 * @property int $sort 排序
 * @property  array imageList  图片列表
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class MerchantZhanhuiFaqModel extends BaseModel
{
    /**
    * @var string
    */
    protected $name = 'merchant_zhanhui_faq';
    protected static bool $isGuid = true;

    protected $json = ['image_list'];

    public const ANSWER_TYPE_RICH_TEXT = 1;
    public const ANSWER_TYPE_IMAGE = 2;
    public const ANSWER_TYPE_LINK = 3;
    public const ANSWER_TYPE_VIDEO = 4;
}
