<?php

declare(strict_types=1);

namespace app\zhanhui\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商家guid
 * @property string $zhanhuiGuid 展会guid
 * @property int $platformUserSysId 用户id
 * @property string $content 留言内容
 * @property string $note 留言备注
 * @property int $adminIsDelete 管理员是否删除：0-未删除；1-已删除
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class MerchantZhanhuiMessageBoardModel extends BaseModel
{
    /**
    * @var string
    */
    protected $name = 'merchant_zhanhui_message_board';
    protected static bool $isGuid = true;
}
