<?php

declare(strict_types=1);

namespace app\zhanhui\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $merchantGuid 商家guid
 * @property string $guid 唯一关键字段
 * @property string $name 展会名称
 * @property string $shortName 展会简称
 * @property string $logo 展会logo
 * @property string $slogo 展会标语
 * @property string $cover 展会封面
 * @property string $description 展会描述
 * @property int $startTime 展会开始时间
 * @property int $endTime 展会结束时间
 * @property int $endIsShow 结束是否展示
 * @property int $aiPoint AI点数
 * @property int $status 状态
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @property int $showOrder 展示顺序
 * @property int $showTime 展示时间
 * @property int $createStatus 展会创建状态：1-等待构建；2-AI生成中；3-已创建
 * @property int $documentSupplements 文档补充次数
 * @property int $parseCount 文档补充次数
 * @mixin BaseModel
 */
class MerchantZhanhuiModel extends BaseModel
{
    /**
    * @var string
    */
    protected $name = 'merchant_zhanhui';
    protected static bool $isGuid = true;

    // 状态
    public const STATUS_NORMAL = 1; // 正常
    public const STATUS_DISABLE = 2; // 禁用

    // 展会创建状态
    public const CREATE_STATUS_WAIT = 1; // 展会创建状态：1-等待构建
    public const CREATE_STATUS_DOING = 2; // 展会创建状态：2-AI生成中
    public const CREATE_STATUS_SUCCESS = 3; // 展会创建状态：3-已创建
}
