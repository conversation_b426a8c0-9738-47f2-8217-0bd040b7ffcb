<?php

declare(strict_types=1);

namespace app\zhanhui\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商家guid
 * @property string $zhanhuiGuid 展会guid
 * @property string $packageGuid 套餐guid
 * @property string $orderNo 订单号
 * @property string $packageName 套餐名称
 * @property float $price 支付金额
 * @property int $parseCount 购买解析次数
 * @property int $payStatus 支付状态：1-待支付，2-已支付，3-支付失败
 * @property int $payType 支付方式：1-微信，2-支付宝
 * @property int $payTime 支付时间
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class MerchantZhanhuiParseOrderModel extends BaseModel
{
    /**
     * @var string
     */
    protected $name = 'merchant_zhanhui_parse_order';
    protected static bool $isGuid = true;

    // 支付状态
    public const PAY_STATUS_WAIT = 1; // 待支付
    public const PAY_STATUS_SUCCESS = 2; // 已支付
    public const PAY_STATUS_FAIL = 3; // 支付失败

    // 支付方式
    public const PAY_TYPE_WECHAT = 1; // 微信
    public const PAY_TYPE_ALIPAY = 2; // 支付宝

    /**
     * 获取支付状态文本
     * @param int $status
     * @return string
     */
    public static function getPayStatusText(int $status): string
    {
        $statusMap = [
            self::PAY_STATUS_WAIT => '待支付',
            self::PAY_STATUS_SUCCESS => '已支付',
            self::PAY_STATUS_FAIL => '支付失败',
        ];

        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取支付方式文本
     * @param int $type
     * @return string
     */
    public static function getPayTypeText(int $type): string
    {
        $typeMap = [
            self::PAY_TYPE_WECHAT => '微信支付',
            self::PAY_TYPE_ALIPAY => '支付宝',
        ];

        return $typeMap[$type] ?? '未知方式';
    }

    /**
     * 生成订单号
     * @return string
     */
    public static function generateOrderNo(): string
    {
        return 'ZH_PARSE_' . date('YmdHis') . mt_rand(1000, 9999);
    }
}
