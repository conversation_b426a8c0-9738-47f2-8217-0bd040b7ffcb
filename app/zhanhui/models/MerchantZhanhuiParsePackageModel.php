<?php

declare(strict_types=1);

namespace app\zhanhui\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $packageName 套餐名称
 * @property float $price 套餐价格（元）
 * @property int $parseCount 解析次数
 * @property int $status 套餐状态：1-启用，2-禁用
 * @property int $sort 排序
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class MerchantZhanhuiParsePackageModel extends BaseModel
{
    /**
     * @var string
     */
    protected $name = 'merchant_zhanhui_parse_package';
    protected static bool $isGuid = true;

    // 套餐状态
    public const STATUS_ENABLE = 1; // 启用
    public const STATUS_DISABLE = 2; // 禁用

    /**
     * 获取状态文本
     * @param int $status
     * @return string
     */
    public static function getStatusText(int $status): string
    {
        $statusMap = [
            self::STATUS_ENABLE => '启用',
            self::STATUS_DISABLE => '禁用',
        ];

        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取启用的套餐列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getEnabledPackages(): array
    {
        // 检查是否存在套餐
        $packages = self::getInstance()
            ->where('status', self::STATUS_ENABLE)
            ->order('sort', 'asc')
            ->select();
        // 没有套餐时自动创建默认套餐列表
        if ($packages->isEmpty()) {
            self::createDefaultPackages();
            // 重新获取套餐列表
            $packages = self::getInstance()
                ->where('status', self::STATUS_ENABLE)
                ->order('sort', 'asc')
                ->select();
        }

        return $packages->toArray();
    }

    /**
     * 创建默认套餐列表
     * @return void
     */
    private static function createDefaultPackages(): void
    {
        // 金额单位：分
        $defaultPackages = [
            [
                'package_name' => '基础套餐',
                'price' => 1000,
                'parse_count' => 10,
                'status' => self::STATUS_ENABLE,
                'sort' => 1,
                'create_time' => time(),
                'update_time' => time(),
            ],
            [
                'package_name' => '标准套餐',
                'price' => 2000,
                'parse_count' => 25,
                'status' => self::STATUS_ENABLE,
                'sort' => 2,
                'create_time' => time(),
                'update_time' => time(),
            ],
            [
                'package_name' => '高级套餐',
                'price' => 5000,
                'parse_count' => 70,
                'status' => self::STATUS_ENABLE,
                'sort' => 3,
                'create_time' => time(),
                'update_time' => time(),
            ],
            [
                'package_name' => '专业套餐',
                'price' => 10000,
                'parse_count' => 150,
                'status' => self::STATUS_ENABLE,
                'sort' => 4,
                'create_time' => time(),
                'update_time' => time(),
            ],
        ];

        // 为每个套餐生成GUID并批量插入
        foreach ($defaultPackages as &$package) {
            $package['guid'] = get_guid();
        }

        self::getInstance()->saveAll($defaultPackages);
    }
}
