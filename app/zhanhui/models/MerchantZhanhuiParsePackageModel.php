<?php

declare(strict_types=1);

namespace app\zhanhui\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $packageName 套餐名称
 * @property float $price 套餐价格（元）
 * @property int $parseCount 解析次数
 * @property int $status 套餐状态：1-启用，2-禁用
 * @property int $sort 排序
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class MerchantZhanhuiParsePackageModel extends BaseModel
{
    /**
     * @var string
     */
    protected $name = 'merchant_zhanhui_parse_package';
    protected static bool $isGuid = true;

    // 套餐状态
    public const STATUS_ENABLE = 1; // 启用
    public const STATUS_DISABLE = 2; // 禁用

    /**
     * 获取状态文本
     * @param int $status
     * @return string
     */
    public static function getStatusText(int $status): string
    {
        $statusMap = [
            self::STATUS_ENABLE => '启用',
            self::STATUS_DISABLE => '禁用',
        ];

        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取启用的套餐列表
     * @return array
     */
    public static function getEnabledPackages(): array
    {
        return self::getInstance()
            ->where('status', self::STATUS_ENABLE)
            ->order('sort', 'asc')
            ->select()
            ->toArray();
    }
}
