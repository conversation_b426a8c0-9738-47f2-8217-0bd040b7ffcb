<?php

declare(strict_types=1);

namespace app\zhanhui\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商家guid
 * @property string $zhanhuiGuid 展会guid
 * @property string $fileName 上传资料文件名称
 * @property string $filePath 上传资料文件路径
 * @property int $supplementType 模块补充类型：1-AI自动识别，2-手动指定模块
 * @property array $targetModules 选择补充资料模块（手动指定时，支持多个模块）
 * @property int $isSyncKnowledge 是否同步导入AI主页知识库：1-是，2-否
 * @property int $runStatus 执行状态：1-等待执行，2-执行中，3-成功，4-执行失败
 * @property string $xinghuoFileId 星火知识库文件id
 * @property string $errorMsg 错误信息
 * @property int $createTime 上传时间
 * @property int $completeTime 执行完成时间
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class MerchantZhanhuiSupplementRecordModel extends BaseModel
{
    /**
     * @var string
     */
    protected $name = 'merchant_zhanhui_supplement_record';
    protected static bool $isGuid = true;

    // 补充类型
    public const SUPPLEMENT_TYPE_AUTO = 1; // AI自动识别
    public const SUPPLEMENT_TYPE_MANUAL = 2; // 手动指定模块

    // 是否同步知识库
    public const SYNC_KNOWLEDGE_YES = 1; // 是
    public const SYNC_KNOWLEDGE_NO = 2; // 否

    // 执行状态
    public const STATUS_RUN_WAIT = 1; // 等待执行
    public const STATUS_RUN_ING = 2; // 执行中
    public const STATUS_RUN_SUCCESS = 3; // 成功
    public const STATUS_RUN_FAIL = 4; // 执行失败

    /**
     * JSON字段
     * @var array
     */
    protected $json = ['target_modules'];

    /**
     * 字段类型转换
     * @var array
     */
    protected $type = [
        'target_modules' => 'array',
    ];

    /**
     * 获取状态文本
     * @param int $status
     * @return string
     */
    public static function getStatusText(int $status): string
    {
        $statusMap = [
            self::STATUS_RUN_WAIT => '等待执行',
            self::STATUS_RUN_ING => '执行中',
            self::STATUS_RUN_SUCCESS => '成功',
            self::STATUS_RUN_FAIL => '执行失败',
        ];

        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 获取补充类型文本
     * @param int $type
     * @return string
     */
    public static function getSupplementTypeText(int $type): string
    {
        $typeMap = [
            self::SUPPLEMENT_TYPE_AUTO => 'AI自动识别',
            self::SUPPLEMENT_TYPE_MANUAL => '手动指定模块',
        ];

        return $typeMap[$type] ?? '未知类型';
    }

    /**
     * 获取同步知识库文本
     * @param int $sync
     * @return string
     */
    public static function getSyncKnowledgeText(int $sync): string
    {
        $syncMap = [
            self::SYNC_KNOWLEDGE_YES => '是',
            self::SYNC_KNOWLEDGE_NO => '否',
        ];

        return $syncMap[$sync] ?? '未知';
    }
}
