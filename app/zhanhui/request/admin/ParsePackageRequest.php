<?php

declare(strict_types=1);

namespace app\zhanhui\request\admin;

use app\libraries\request\BaseRequest;

class ParsePackageRequest extends BaseRequest
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'guid' => 'length:32',
        'packageName' => 'require|length:1,100',
        'price' => 'require|float|egt:0',
        'parseCount' => 'require|integer|egt:1',
        'status' => 'in:1,2',
        'sort' => 'integer|egt:0',
        'page' => 'integer|egt:1',
        'limit' => 'integer|between:1,100',
    ];

    /**
     * 验证消息
     * @var array
     */
    protected $message = [
        'guid.length' => 'GUID格式错误',
        'packageName.require' => '套餐名称不能为空',
        'packageName.length' => '套餐名称长度不能超过100个字符',
        'price.require' => '套餐价格不能为空',
        'price.float' => '套餐价格必须是数字',
        'price.egt' => '套餐价格不能小于0',
        'parseCount.require' => '解析次数不能为空',
        'parseCount.integer' => '解析次数必须是整数',
        'parseCount.egt' => '解析次数必须大于0',
        'status.in' => '状态参数错误',
        'sort.integer' => '排序必须是整数',
        'sort.egt' => '排序不能小于0',
        'page.integer' => '页码必须是整数',
        'page.egt' => '页码必须大于等于1',
        'limit.integer' => '每页数量必须是整数',
        'limit.between' => '每页数量必须在1-100之间',
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'create' => ['packageName', 'price', 'parseCount', 'status', 'sort'],
        'update' => ['guid', 'packageName', 'price', 'parseCount', 'status', 'sort'],
        'delete' => ['guid'],
        'detail' => ['guid'],
        'status' => ['guid', 'status'],
        'list' => ['page', 'limit'],
    ];

    /**
     * 创建套餐验证
     * @return ParsePackageRequest
     */
    public function sceneCreate(): ParsePackageRequest
    {
        return $this->only(['packageName', 'price', 'parseCount', 'status', 'sort']);
    }

    /**
     * 更新套餐验证
     * @return ParsePackageRequest
     */
    public function sceneUpdate(): ParsePackageRequest
    {
        return $this->only(['guid', 'packageName', 'price', 'parseCount', 'status', 'sort']);
    }

    /**
     * 删除套餐验证
     * @return ParsePackageRequest
     */
    public function sceneDelete(): ParsePackageRequest
    {
        return $this->only(['guid']);
    }

    /**
     * 套餐详情验证
     * @return ParsePackageRequest
     */
    public function sceneDetail(): ParsePackageRequest
    {
        return $this->only(['guid']);
    }

    /**
     * 状态切换验证
     * @return ParsePackageRequest
     */
    public function sceneStatus(): ParsePackageRequest
    {
        return $this->only(['guid', 'status']);
    }

    /**
     * 列表验证
     * @return ParsePackageRequest
     */
    public function sceneList(): ParsePackageRequest
    {
        return $this->only(['page', 'limit']);
    }
}
