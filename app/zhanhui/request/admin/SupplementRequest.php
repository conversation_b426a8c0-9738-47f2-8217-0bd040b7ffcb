<?php

declare(strict_types=1);

namespace app\zhanhui\request\admin;

use app\libraries\request\BaseRequest;

class SupplementRequest extends BaseRequest
{
    /**
     * 验证规则
     * @var array
     */
    protected $rule = [
        'zhanhuiGuid' => 'require|length:32',
        'file' => 'require|file',
        'supplementType' => 'require|in:1,2',
        'targetModules' => 'array',
        'isSyncKnowledge' => 'in:1,2',
        'page' => 'integer|egt:1',
        'limit' => 'integer|between:1,100',
        'guid' => 'length:32',
    ];

    /**
     * 验证消息
     * @var array
     */
    protected $message = [
        'zhanhuiGuid.require' => '展会GUID不能为空',
        'zhanhuiGuid.length' => '展会GUID格式错误',
        'file.require' => '请选择要上传的文件',
        'file.file' => '上传的文件格式不正确',
        'supplementType.require' => '补充类型不能为空',
        'supplementType.in' => '补充类型参数错误',
        'targetModules.array' => '目标模块必须是数组格式',
        'isSyncKnowledge.in' => '同步知识库参数错误',
        'page.integer' => '页码必须是整数',
        'page.egt' => '页码必须大于等于1',
        'limit.integer' => '每页数量必须是整数',
        'limit.between' => '每页数量必须在1-100之间',
        'guid.length' => 'GUID格式错误',
    ];

    /**
     * 验证场景
     * @var array
     */
    protected $scene = [
        'upload' => ['zhanhuiGuid', 'file', 'supplementType', 'targetModules', 'isSyncKnowledge'],
        'history' => ['zhanhuiGuid', 'page', 'limit'],
        'detail' => ['guid'],
    ];

    /**
     * 上传文档验证
     * @return SupplementRequest
     */
    public function sceneUpload(): SupplementRequest
    {
        return $this->only(['zhanhuiGuid', 'file', 'supplementType', 'targetModules', 'isSyncKnowledge'])
            ->append('targetModules', function ($value, $data) {
                // 如果是手动指定模块，targetModules不能为空
                if ($data['supplementType'] == 2 && empty($value)) {
                    return '手动指定模块时，目标模块不能为空';
                }
                return true;
            });
    }

    /**
     * 历史记录验证
     * @return SupplementRequest
     */
    public function sceneHistory(): SupplementRequest
    {
        return $this->only(['zhanhuiGuid', 'page', 'limit']);
    }

    /**
     * 详情验证
     * @return SupplementRequest
     */
    public function sceneDetail(): SupplementRequest
    {
        return $this->only(['guid']);
    }
}
