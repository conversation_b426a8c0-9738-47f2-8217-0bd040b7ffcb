<?php

namespace app\zhanhui\task;

use app\libraries\service\ai_workflow\prompt\PromptBuildService;
use app\libraries\service\ai_workflow\tools\AzureAiTool;
use app\libraries\service\ai_workflow\tools\ZhipuTool;
use app\libraries\service\dify\XinghuoDatabaseService;
use app\libraries\service\dify\DifyDatabaseService;
use app\zhanhui\cache\ZhanhuiCache;
use app\zhanhui\models\MerchantZhanhuiSupplementRecordModel;
use app\zhanhui\models\MerchantZhanhuiModel;
use app\zhanhui\models\MerchantZhanhuiConfigModel;
use app\zhanhui\models\MerchantZhanhuiFaqModel;
use app\merchant\models\MerchantKnowledgeBaseModel;
use Cls\Log;
use yunwuxin\cron\Task;

class ZhanhuiBcAiBuildTask extends Task
{
    private $xinghuoDatabaseService;

    public function execute()
    {
        $xinghuoDatabaseService = new XinghuoDatabaseService();
        $this->xinghuoDatabaseService = $xinghuoDatabaseService;
        
        // 获取待处理的补充记录
        $runList = MerchantZhanhuiSupplementRecordModel::getInstance()
            ->where('run_status', MerchantZhanhuiSupplementRecordModel::STATUS_RUN_WAIT)
            ->limit(10)
            ->select();
            
        foreach ($runList as $record) {
            $lockKey = 'zhanhui_bc_ai_build_lock_' . $record->guid;
            $lock = ZhanhuiCache::getInstance()->lock($lockKey, 120);
            if (!$lock) {
                var_dump('获取锁失败');
                continue;
            }
            
            LogInfo('ZhanhuiBcAiBuildTask', '展会补充AI构建任务', '开始处理补充记录', [
                'record' => $record->toArray(),
            ]);
            
            $this->processSupplementRecord($record);
            
            ZhanhuiCache::getInstance()->unlock($lockKey, $lock);
        }
    }

    /**
     * 处理补充记录
     * @param MerchantZhanhuiSupplementRecordModel $record
     */
    private function processSupplementRecord($record)
    {
        try {
            // 更新状态为执行中
            $record->run_status = MerchantZhanhuiSupplementRecordModel::STATUS_RUN_ING;
            $record->save();

            // 1. 检查是否需要同步知识库
            if ($record->is_sync_knowledge == MerchantZhanhuiSupplementRecordModel::SYNC_KNOWLEDGE_YES) {
                // 检查展会知识库，不存在则创建
                $this->ensureKnowledgeBase($record);
                // 将文档上传到知识库
                $this->uploadToKnowledgeBase($record);
            }

            // 2. 上传文档到星火知识库进行分析
            $record = $this->uploadToXinghuoDatabase($record);

            // 3. AI识别文档内容所属模块并生成FAQ
            $this->identifyModulesAndGenerateFAQs($record);

            // 4. 消耗解析次数
            $this->consumeParseCount($record);

            // 5. 更新记录状态为成功
            $record->run_status = MerchantZhanhuiSupplementRecordModel::STATUS_RUN_SUCCESS;
            $record->complete_time = time();
            $record->save();

            LogInfo('ZhanhuiBcAiBuildTask', '展会补充AI构建任务', '处理完成', [
                'recordGuid' => $record->guid,
            ]);

        } catch (\Exception $e) {
            // 处理失败，更新状态
            $record->run_status = MerchantZhanhuiSupplementRecordModel::STATUS_RUN_FAIL;
            $record->error_msg = $e->getMessage();
            $record->save();

            LogInfo('ZhanhuiBcAiBuildTask', '展会补充AI构建任务', '处理失败', [
                'recordGuid' => $record->guid,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }

    /**
     * 确保展会知识库存在
     * @param MerchantZhanhuiSupplementRecordModel $record
     */
    private function ensureKnowledgeBase($record)
    {
        // 检查展会配置中是否已绑定知识库
        $zhanhuiConfig = MerchantZhanhuiConfigModel::getInstance()
            ->where('zhanhui_guid', $record->zhanhui_guid)
            ->findOrEmpty();

        if ($zhanhuiConfig->isEmpty()) {
            throw new \Exception('展会配置不存在');
        }

        // 如果已经绑定知识库，直接返回
        if (!empty($zhanhuiConfig->merchant_knowledge_guid)) {
            $existKnowledge = MerchantKnowledgeBaseModel::getInstance()
                ->where('guid', $zhanhuiConfig->merchant_knowledge_guid)
                ->findOrEmpty();
            if (!$existKnowledge->isEmpty()) {
                return;
            }
        }

        // 创建新的知识库
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $record->zhanhui_guid)
            ->findOrEmpty();

        if ($zhanhuiInfo->isEmpty()) {
            throw new \Exception('展会信息不存在');
        }

        // 创建dify知识库
        $difyService = DifyDatabaseService::getInstance();
        $difyResult = $difyService->createDatabase($zhanhuiInfo->name . '_知识库');
        
        if ($difyResult['status_code'] != 200 || empty($difyResult['response_data'])) {
            throw new \Exception('知识库创建失败');
        }

        $difyResponseData = json_decode($difyResult['response_data'], true);

        // 保存知识库信息
        $knowledgeBase = new MerchantKnowledgeBaseModel();
        $knowledgeBase->merchant_guid = $record->merchant_guid;
        $knowledgeBase->knowledge_title = $zhanhuiInfo->name . '_知识库';
        $knowledgeBase->dify_base_id = $difyResponseData['id'];
        $knowledgeBase->save();

        // 绑定到展会配置
        $zhanhuiConfig->merchant_knowledge_guid = $knowledgeBase->guid;
        $zhanhuiConfig->save();

        LogInfo('ZhanhuiBcAiBuildTask', '展会补充AI构建任务', '创建知识库成功', [
            'zhanhuiGuid' => $record->zhanhui_guid,
            'knowledgeGuid' => $knowledgeBase->guid,
        ]);
    }

    /**
     * 上传文档到知识库
     * @param MerchantZhanhuiSupplementRecordModel $record
     */
    private function uploadToKnowledgeBase($record)
    {
        // 获取展会绑定的知识库
        $zhanhuiConfig = MerchantZhanhuiConfigModel::getInstance()
            ->where('zhanhui_guid', $record->zhanhui_guid)
            ->findOrEmpty();

        if ($zhanhuiConfig->isEmpty() || empty($zhanhuiConfig->merchant_knowledge_guid)) {
            throw new \Exception('展会未绑定知识库');
        }

        $knowledgeBase = MerchantKnowledgeBaseModel::getInstance()
            ->where('guid', $zhanhuiConfig->merchant_knowledge_guid)
            ->findOrEmpty();

        if ($knowledgeBase->isEmpty()) {
            throw new \Exception('知识库不存在');
        }

        // 上传文档到dify知识库
        $difyService = DifyDatabaseService::getInstance();
        $uploadResult = $difyService->createDocumentByFile(
            $knowledgeBase->dify_base_id,
            $record->file_path
        );

        if ($uploadResult['status_code'] != 200) {
            throw new \Exception('文档上传到知识库失败');
        }

        LogInfo('ZhanhuiBcAiBuildTask', '展会补充AI构建任务', '文档上传到知识库成功', [
            'recordGuid' => $record->guid,
            'knowledgeBaseId' => $knowledgeBase->dify_base_id,
        ]);
    }

    /**
     * 上传文档到星火知识库
     * @param MerchantZhanhuiSupplementRecordModel $record
     */
    private function uploadToXinghuoDatabase($record)
    {
        if (!empty($record->xinghuo_file_id)) {
            return; // 已经上传过了
        }

        // 上传文件到星火知识库，用于后续的文档知识提取
        $result = $this->xinghuoDatabaseService->uploadFile($record->file_path);
        if (!$result || empty($result['fileId']) || $result['fileStatus'] == 'failed') {
            throw new \Exception('文件上传到星火知识库失败');
        }

        $record->xinghuo_file_id = $result['fileId'];
        $record->save();

        // 等待20秒，等待文件向量化完成
        sleep(20);

        LogInfo('ZhanhuiBcAiBuildTask', '展会补充AI构建任务', '文档上传到星火知识库成功', [
            'recordGuid' => $record->guid,
            'xinghuoFileId' => $result['fileId'],
        ]);

        return $record;
    }

    /**
     * AI识别模块并生成FAQ
     * @param MerchantZhanhuiSupplementRecordModel $record
     */
    private function identifyModulesAndGenerateFAQs($record)
    {
        // 获取展会配置信息，获取模块信息
        $zhanhuiConfig = MerchantZhanhuiConfigModel::getInstance()
            ->where('zhanhui_guid', $record->zhanhui_guid)
            ->findOrEmpty();

        if ($zhanhuiConfig->isEmpty()) {
            throw new \Exception('展会配置不存在');
        }

        // 构建模块信息数组（包含模块名称和描述）
        $faqCates = [
            [
                'name' => $zhanhuiConfig->topic_banner_title,
                'desc' => '展会议程、活动安排、时间表相关内容'
            ],
            [
                'name' => $zhanhuiConfig->recommend_company_title,
                'desc' => '参展企业、公司介绍、产品展示相关内容'
            ],
            [
                'name' => $zhanhuiConfig->guest_banner_title,
                'desc' => '演讲嘉宾、专家介绍、人物相关内容'
            ]
        ];

        // 如果是手动指定模块，只处理指定的模块
        $targetModules = [];
        if ($record->supplement_type == MerchantZhanhuiSupplementRecordModel::SUPPLEMENT_TYPE_MANUAL 
            && !empty($record->target_modules)) {
            foreach ($record->target_modules as $targetModule) {
                foreach ($faqCates as $cate) {
                    if ($cate['name'] == $targetModule) {
                        $targetModules[] = $cate;
                        break;
                    }
                }
            }
            $faqCates = $targetModules;
        }

        $this->generateFAQsByModules($record, $faqCates);
    }

    /**
     * 根据模块生成FAQ
     * @param MerchantZhanhuiSupplementRecordModel $record
     * @param array $faqCates
     */
    private function generateFAQsByModules($record, $faqCates)
    {
        // 获取星火文档切分的文档片段
        $fileChunkLists = $this->xinghuoDatabaseService->fileChunkLists($record->xinghuo_file_id);
        
        // 为减少AI的调用次数，1次取3段内容中的content作为一次请求
        $mergeChunks = [];
        $chunkCount = count($fileChunkLists);
        $chunkIndex = 0;
        $chunkSize = 3;
        while ($chunkIndex < $chunkCount) {
            $chunks = array_slice($fileChunkLists, $chunkIndex, $chunkSize);
            $chunkIndex += $chunkSize;
            $content = '';
            foreach ($chunks as $chunk) {
                $content .= $chunk['content'];
            }
            $mergeChunks[] = $content;
        }

        // 构建模块分类字符串，包含名称和描述
        $faqCatesStr = '';
        $faqPositionByCate = [];
        foreach ($faqCates as $cate) {
            $faqCatesStr .= $cate['name'] . '(' . $cate['desc'] . '),';
            $faqPositionByCate[$cate['name']] = 2; // 轮播图位置
        }
        $faqCatesStr = rtrim($faqCatesStr, ',');

        // 问题提取的最大次数
        $maxCount = 0;
        $addZhanhuiFaqData = [];
        
        foreach ($mergeChunks as $index => $chunk) {
            if ($maxCount >= 20) { // 限制处理次数
                break;
            }
            
            $prompt = PromptBuildService::getInstance()->zhanhuiFaqs($chunk, $faqCatesStr);
            $result = AzureAiTool::getInstance()->gpt4oChatComplete($prompt, 'json');
            $maxCount++;
            
            // 休息0.2秒
            usleep(200000);
            
            $faqs = json_decode($result, true);
            LogInfo('ZhanhuiBcAiBuildTask', '展会补充AI构建任务', '常见问题提取', [
                'recordGuid' => $record->guid,
                'faqs' => $faqs
            ]);

            // 工具图标随机获取
            $iconType = ['notice', 'msg', 'hot', 'recommend', 'tool'];
            
            if (!empty($faqs['questions']) && count($faqs['questions']) > 0) {
                foreach ($faqs['questions'] as $faq) {
                    $faqCate = $faq['cate'] ?? '其他';
                    
                    // 只有识别到对应模块才添加内容
                    if (isset($faqPositionByCate[$faqCate])) {
                        $addZhanhuiFaqData[] = [
                            'guid' => get_guid(),
                            'merchant_guid' => $record->merchant_guid,
                            'zhanhui_guid' => $record->zhanhui_guid,
                            'question' => $faq['question'],
                            'ai_chat_question' => $faq['question'],
                            'answer_type' => MerchantZhanhuiFaqModel::ANSWER_TYPE_RICH_TEXT,
                            'answer' => $faq['answer'],
                            'position' => $faqPositionByCate[$faqCate],
                            'icon_type' => $iconType[array_rand($iconType)],
                            'create_time' => time(),
                        ];
                    }
                }
            }
        }

        // 批量保存FAQ
        if (!empty($addZhanhuiFaqData)) {
            $faqModel = new MerchantZhanhuiFaqModel();
            $faqModel->saveAll($addZhanhuiFaqData);
            
            LogInfo('ZhanhuiBcAiBuildTask', '展会补充AI构建任务', 'FAQ生成完成', [
                'recordGuid' => $record->guid,
                'faqCount' => count($addZhanhuiFaqData),
            ]);
        }
    }

    /**
     * 消耗解析次数
     * @param MerchantZhanhuiSupplementRecordModel $record
     */
    private function consumeParseCount($record)
    {
        // 减少展会解析次数
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $record->zhanhui_guid)
            ->findOrEmpty();

        if (!$zhanhuiInfo->isEmpty() && $zhanhuiInfo->parse_count > 0) {
            $zhanhuiInfo->parse_count -= 1;
            $zhanhuiInfo->save();
            
            LogInfo('ZhanhuiBcAiBuildTask', '展会补充AI构建任务', '消耗解析次数', [
                'zhanhuiGuid' => $record->zhanhui_guid,
                'remainingCount' => $zhanhuiInfo->parse_count,
            ]);
        }
    }
}
