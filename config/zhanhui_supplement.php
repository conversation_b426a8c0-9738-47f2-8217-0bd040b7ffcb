<?php

/**
 * 展会补充文档功能配置
 */

return [
    // 文件上传配置
    'upload' => [
        'path' => 'uploads/zhanhui/supplement/',
        'max_size' => 50 * 1024 * 1024, // 50MB
        'allowed_types' => ['pdf', 'doc', 'docx', 'txt', 'md'],
    ],

    // AI处理配置
    'ai' => [
        'max_chunks_per_request' => 3, // 每次请求处理的文档片段数
        'max_process_count' => 20, // 最大处理次数
        'sleep_time' => 200000, // 请求间隔时间（微秒）
    ],

    // 知识库配置
    'knowledge' => [
        'auto_create' => true, // 是否自动创建知识库
        'default_sync' => true, // 默认是否同步到知识库
    ],

    // 套餐配置（如果数据库中没有套餐时的默认配置）
    'default_packages' => [
        [
            'package_name' => '基础套餐',
            'price' => 10.00,
            'parse_count' => 10,
            'status' => 1,
            'sort' => 1,
        ],
        [
            'package_name' => '标准套餐',
            'price' => 20.00,
            'parse_count' => 25,
            'status' => 1,
            'sort' => 2,
        ],
        [
            'package_name' => '高级套餐',
            'price' => 50.00,
            'parse_count' => 70,
            'status' => 1,
            'sort' => 3,
        ],
        [
            'package_name' => '专业套餐',
            'price' => 100.00,
            'parse_count' => 150,
            'status' => 1,
            'sort' => 4,
        ],
    ],

    // 定时任务配置
    'cron' => [
        'enabled' => true,
        'interval' => 60, // 执行间隔（秒）
        'max_concurrent' => 5, // 最大并发处理数
    ],

    // 支付配置
    'payment' => [
        'notify_url' => '/api/zhanhui/supplement/pay-notify',
        'timeout' => 3600, // 支付超时时间（秒）
    ],
];
