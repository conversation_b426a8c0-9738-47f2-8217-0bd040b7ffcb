# 展会补充文档功能相关数据表

# 1. 展会补充文档记录表
CREATE TABLE merchant_zhanhui_supplement_record (
    sys_id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    guid CHAR(32) NOT NULL COMMENT '唯一关键字段',
    merchant_guid CHAR(32) NOT NULL COMMENT '商家guid',
    zhanhui_guid CHAR(32) NOT NULL COMMENT '展会guid',
    file_name VARCHAR(255) NOT NULL DEFAULT '' COMMENT '上传资料文件名称',
    file_path VARCHAR(500) NOT NULL DEFAULT '' COMMENT '上传资料文件路径',
    supplement_type TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '模块补充类型：1-AI自动识别，2-手动指定模块',
    target_modules JSON COMMENT '选择补充资料模块（手动指定时，支持多个模块）',
    is_sync_knowledge TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否同步导入AI主页知识库：1-是，2-否',
    run_status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '执行状态：1-等待执行，2-执行中，3-成功，4-执行失败',
    xinghuo_file_id VARCHAR(255) NOT NULL DEFAULT '' COMMENT '星火知识库文件id',
    error_msg TEXT COMMENT '错误信息',
    create_time INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '上传时间',
    complete_time INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '执行完成时间',
    update_time INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    deleted_at INT UNSIGNED DEFAULT NULL COMMENT '删除时间',
    modify_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY zhanhui_guid (zhanhui_guid),
    KEY merchant_guid (merchant_guid),
    KEY guid (guid)
) COMMENT '展会补充文档记录表';

# 2. 解析次数套餐表
CREATE TABLE merchant_zhanhui_parse_package (
    sys_id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    guid CHAR(32) NOT NULL COMMENT '唯一关键字段',
    package_name VARCHAR(100) NOT NULL DEFAULT '' COMMENT '套餐名称',
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '套餐价格（元）',
    parse_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '解析次数',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '套餐状态：1-启用，2-禁用',
    sort INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
    create_time INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    update_time INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    deleted_at INT UNSIGNED DEFAULT NULL COMMENT '删除时间',
    modify_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY guid (guid),
    KEY status (status)
) COMMENT '解析次数套餐表';

# 3. 解析次数购买记录表
CREATE TABLE merchant_zhanhui_parse_order (
    sys_id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    guid CHAR(32) NOT NULL COMMENT '唯一关键字段',
    merchant_guid CHAR(32) NOT NULL COMMENT '商家guid',
    zhanhui_guid CHAR(32) NOT NULL COMMENT '展会guid',
    package_guid CHAR(32) NOT NULL COMMENT '套餐guid',
    order_no VARCHAR(100) NOT NULL DEFAULT '' COMMENT '订单号',
    package_name VARCHAR(100) NOT NULL DEFAULT '' COMMENT '套餐名称',
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
    parse_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '购买解析次数',
    pay_status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付状态：1-待支付，2-已支付，3-支付失败',
    pay_type TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付方式：1-微信，2-支付宝',
    pay_time INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付时间',
    create_time INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    update_time INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    deleted_at INT UNSIGNED DEFAULT NULL COMMENT '删除时间',
    modify_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY order_no (order_no),
    KEY zhanhui_guid (zhanhui_guid),
    KEY guid (guid)
) COMMENT '解析次数购买记录表';

# 4. 展会表新增字段
ALTER TABLE merchant_zhanhui ADD COLUMN parse_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '文档补充次数';

# 5. 初始化默认套餐数据
INSERT INTO merchant_zhanhui_parse_package (guid, package_name, price, parse_count, status, sort, create_time, update_time) VALUES
(REPLACE(UUID(), '-', ''), '基础套餐', 10.00, 10, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(REPLACE(UUID(), '-', ''), '标准套餐', 20.00, 25, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(REPLACE(UUID(), '-', ''), '高级套餐', 50.00, 70, 1, 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(REPLACE(UUID(), '-', ''), '专业套餐', 100.00, 150, 1, 4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
